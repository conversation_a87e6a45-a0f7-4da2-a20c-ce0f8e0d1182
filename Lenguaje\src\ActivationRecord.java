import java.util.HashMap;
import java.util.Map;

/**
 * Registro de Activación para el manejo de llamadas a funciones.
 * Cada instancia representa el contexto de ejecución de una función.
 */
public class ActivationRecord {
    
    // Dirección de retorno (índice del cuádruplo al que regresar)
    private int returnAddress;
    
    // Memoria local de la función (parámetros y variables locales)
    private Map<Integer, Object> localMemory;
    
    // ID o nombre de la función
    private String functionName;
    
    // Dirección base para variables locales
    private int baseAddress;
    
    /**
     * Constructor del registro de activación.
     * @param functionName Nombre de la función
     * @param returnAddress Dirección de retorno
     * @param baseAddress Dirección base para variables locales
     */
    public ActivationRecord(String functionName, int returnAddress, int baseAddress) {
        this.functionName = functionName;
        this.returnAddress = returnAddress;
        this.baseAddress = baseAddress;
        this.localMemory = new HashMap<>();
    }
    
    /**
     * Obtiene la dirección de retorno.
     * @return Dirección de retorno
     */
    public int getReturnAddress() {
        return returnAddress;
    }
    
    /**
     * Establece la dirección de retorno.
     * @param returnAddress Nueva dirección de retorno
     */
    public void setReturnAddress(int returnAddress) {
        this.returnAddress = returnAddress;
    }
    
    /**
     * Obtiene el nombre de la función.
     * @return Nombre de la función
     */
    public String getFunctionName() {
        return functionName;
    }
    
    /**
     * Obtiene la dirección base para variables locales.
     * @return Dirección base
     */
    public int getBaseAddress() {
        return baseAddress;
    }
    
    /**
     * Obtiene un valor de la memoria local.
     * @param address Dirección virtual local
     * @return Valor almacenado
     */
    public Object getLocalValue(int address) {
        return localMemory.get(address);
    }
    
    /**
     * Establece un valor en la memoria local.
     * @param address Dirección virtual local
     * @param value Valor a almacenar
     */
    public void setLocalValue(int address, Object value) {
        localMemory.put(address, value);
    }
    
    /**
     * Verifica si una dirección local está inicializada.
     * @param address Dirección virtual local
     * @return true si está inicializada, false en caso contrario
     */
    public boolean isLocalInitialized(int address) {
        return localMemory.containsKey(address) && localMemory.get(address) != null;
    }
    
    /**
     * Obtiene una copia de la memoria local.
     * @return Mapa con la memoria local
     */
    public Map<Integer, Object> getLocalMemory() {
        return new HashMap<>(localMemory);
    }
    
    /**
     * Limpia la memoria local.
     * Útil al finalizar la ejecución de la función.
     */
    public void clearLocalMemory() {
        localMemory.clear();
    }
    
    /**
     * Establece un parámetro en la memoria local.
     * @param paramIndex Índice del parámetro (0, 1, 2, ...)
     * @param value Valor del parámetro
     */
    public void setParameter(int paramIndex, Object value) {
        // Los parámetros se almacenan en direcciones consecutivas a partir de la base
        int paramAddress = baseAddress + paramIndex;
        localMemory.put(paramAddress, value);
    }
    
    /**
     * Obtiene un parámetro de la memoria local.
     * @param paramIndex Índice del parámetro
     * @return Valor del parámetro
     */
    public Object getParameter(int paramIndex) {
        int paramAddress = baseAddress + paramIndex;
        return localMemory.get(paramAddress);
    }
    
    /**
     * Imprime el estado del registro de activación para depuración.
     */
    public void printState() {
        System.out.println("=== Registro de Activación: " + functionName + " ===");
        System.out.println("Dirección de retorno: " + returnAddress);
        System.out.println("Dirección base: " + baseAddress);
        System.out.println("Memoria local:");
        for (Map.Entry<Integer, Object> entry : localMemory.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
        System.out.println("=======================================");
    }
    
    @Override
    public String toString() {
        return "ActivationRecord{" +
                "functionName='" + functionName + '\'' +
                ", returnAddress=" + returnAddress +
                ", baseAddress=" + baseAddress +
                ", localMemory=" + localMemory +
                '}';
    }
}
