# Manual de VirtualMemory.java

## Propósito General
La clase `VirtualMemory` es el administrador de memoria virtual del compilador. Su función principal es asignar direcciones virtuales únicas a variables, constantes y temporales, organizándolas en segmentos específicos según su tipo y ámbito. Actúa como el sistema de gestión de direcciones que permite la traducción de nombres simbólicos a direcciones numéricas.

## Concepto de Memoria Virtual
La memoria virtual organiza el espacio de direcciones en segmentos bien definidos:
- **Segmentación por Ámbito**: Global vs Local
- **Segmentación por Tipo**: int, float, string, bool
- **Segmentación por Uso**: Variables, Temporales, Constantes
- **Direcciones Únicas**: Cada elemento tiene una dirección única e inmutable

## Arquitectura de Segmentos

### Rangos de Direcciones Definidos
```java
public static final int GLOBAL_INT_START = 1000;      // Variables globales enteras
public static final int GLOBAL_FLOAT_START = 2000;    // Variables globales flotantes
public static final int GLOBAL_STRING_START = 3000;   // Variables globales string
public static final int LOCAL_INT_START = 4000;       // Variables locales enteras
public static final int LOCAL_FLOAT_START = 5000;     // Variables locales flotantes
public static final int LOCAL_STRING_START = 6000;    // Variables locales string
public static final int TEMP_INT_START = 7000;        // Temporales enteras
public static final int TEMP_FLOAT_START = 8000;      // Temporales flotantes
public static final int TEMP_STRING_START = 9000;     // Temporales string
public static final int TEMP_BOOL_START = 10000;      // Temporales booleanas
public static final int CTE_INT_START = 11000;        // Constantes enteras
public static final int CTE_FLOAT_START = 12000;      // Constantes flotantes
public static final int CTE_STRING_START = 13000;     // Constantes string
```

### Organización por Segmentos
- **1000-3999**: Variables Globales (por tipo)
- **4000-6999**: Variables Locales (por tipo)
- **7000-10999**: Variables Temporales (por tipo)
- **11000+**: Constantes (por tipo)

## Estructura de Datos

### Contadores de Direcciones
```java
private int globalIntCounter = GLOBAL_INT_START;
private int globalFloatCounter = GLOBAL_FLOAT_START;
private int globalStringCounter = GLOBAL_STRING_START;
private int localIntCounter = LOCAL_INT_START;
private int localFloatCounter = LOCAL_FLOAT_START;
private int localStringCounter = LOCAL_STRING_START;
private int tempIntCounter = TEMP_INT_START;
private int tempFloatCounter = TEMP_FLOAT_START;
private int tempStringCounter = TEMP_STRING_START;
private int tempBoolCounter = TEMP_BOOL_START;
private int cteIntCounter = CTE_INT_START;
private int cteFloatCounter = CTE_FLOAT_START;
private int cteStringCounter = CTE_STRING_START;
```

### Mapas de Direcciones
```java
private Map<String, Integer> variableAddresses = new HashMap<>();    // Variables → Direcciones
private Map<String, Integer> constantAddresses = new HashMap<>();    // Constantes → Direcciones
private Map<String, Integer> tempAddresses = new HashMap<>();        // Temporales → Direcciones
```

### Variables de Control
```java
private int currentParameterCount = 0;  // Número de parámetros en función actual
```

## Métodos Principales

### getVariableAddress()
```java
public int getVariableAddress(String name, String type, boolean isGlobal)
```

**Propósito**: Asignar dirección virtual a una variable

**Flujo de Ejecución**:
1. **Verificar Existencia**: Si ya tiene dirección, la retorna
2. **Validar Tipo**: Si es desconocido, asume "int" por defecto
3. **Determinar Segmento**: Global vs Local según parámetro
4. **Asignar Dirección**: Según tipo y ámbito
5. **Registrar**: Almacena en mapa de direcciones
6. **Retornar**: Dirección asignada

**Lógica de Asignación**:
```java
if (isGlobal) {
    switch (type) {
        case "int": address = globalIntCounter++; break;
        case "float": address = globalFloatCounter++; break;
        case "str": address = globalStringCounter++; break;
        default: address = globalIntCounter++; break;
    }
} else {
    switch (type) {
        case "int": address = localIntCounter++; break;
        case "float": address = localFloatCounter++; break;
        case "str": address = localStringCounter++; break;
        default: address = localIntCounter++; break;
    }
}
```

### getConstantAddress()
```java
public int getConstantAddress(String value, String type)
```

**Propósito**: Asignar dirección virtual a una constante

**Características Especiales**:
- **Deduplicación**: Misma constante siempre tiene misma dirección
- **Inferencia de Tipo**: Puede inferir tipo del valor si no se proporciona
- **Validación**: Patrones regex para identificar tipos

**Inferencia de Tipos**:
```java
if (value.matches("\\d+")) type = "int";
else if (value.matches("\\d+\\.\\d+")) type = "float";
else if (value.startsWith("\"") && value.endsWith("\"")) type = "str";
else if (value.equals("true") || value.equals("false")) type = "bool";
```

### getTempAddress()
```java
public int getTempAddress(String type)
```

**Propósito**: Asignar dirección a variable temporal anónima

**Generación de Nombres**: Crea nombres automáticos "t0", "t1", "t2", etc.

### getTempAddressForName()
```java
public int getTempAddressForName(String tempName, String type)
```

**Propósito**: Asignar dirección a variable temporal con nombre específico

**Uso**: Cuando GeneradorCuadruplos crea temporales con nombres específicos

## Gestión de Funciones

### setParameterCount()
```java
public void setParameterCount(int paramCount)
```

**Propósito**: Configurar número de parámetros para función actual

**Efecto en Contadores**:
```java
localIntCounter = LOCAL_INT_START + paramCount;
localFloatCounter = LOCAL_FLOAT_START + paramCount;
localStringCounter = LOCAL_STRING_START + paramCount;
```

**Razón**: Los parámetros ocupan las primeras direcciones locales, las variables locales empiezan después

### getParameterCount()
- Retorna el número de parámetros configurado
- Usado para cálculos de direcciones

### resetLocalAndTemp()
```java
public void resetLocalAndTemp()
```

**Propósito**: Reiniciar contadores locales y temporales

**Uso**: Al cambiar de contexto entre funciones

**Efecto**: Restaura contadores a valores iniciales de segmento

## Manejo de Tipos

### Tipos Soportados
- **"int"**: Números enteros
- **"float"**: Números flotantes
- **"str"**: Cadenas de texto
- **"bool"**: Valores booleanos (almacenados como int)

### Manejo de Tipos Desconocidos
- **Estrategia**: Asumir "int" por defecto
- **Logging**: Advertencias para tipos no reconocidos
- **Robustez**: Continúa ejecución con tipo por defecto

### Casos Especiales
- **Booleanos**: Se almacenan en segmento de enteros para constantes
- **Temporales Booleanas**: Tienen su propio segmento
- **Strings**: Incluyen comillas en el valor para constantes

## Flujo de Uso Típico

### Durante Análisis Semántico
1. **Variables Globales**: Se asignan direcciones en segmento global
2. **Declaración de Funciones**: Se configura número de parámetros
3. **Variables Locales**: Se asignan después de parámetros

### Durante Generación de Cuádruplos
1. **Constantes**: Se asignan direcciones según aparecen
2. **Temporales**: Se crean para resultados intermedios
3. **Referencias**: Se obtienen direcciones existentes

### Durante Ejecución
1. **Lectura**: ExecutionMemory y ActivationRecord usan direcciones
2. **Escritura**: Mismas direcciones para almacenar resultados

## Interacción con Otros Componentes

### Con SemanticVisitor
- **Variables**: SemanticVisitor valida, VirtualMemory asigna direcciones
- **Ámbitos**: Coordinación para determinar global vs local

### Con GeneradorCuadruplos
- **Temporales**: GeneradorCuadruplos solicita direcciones para resultados
- **Constantes**: Asignación automática durante generación
- **Parámetros**: Configuración para funciones

### Con ExecutionMemory
- **Rangos**: ExecutionMemory usa rangos definidos por VirtualMemory
- **Segmentación**: Coordinación para determinar tipo de dirección

### Con ActivationRecord
- **Direcciones Locales**: ActivationRecord maneja direcciones en rango local
- **Parámetros**: Direcciones consecutivas desde LOCAL_INT_START

## Consideraciones de Diseño

### Eficiencia
- **HashMap**: Acceso O(1) para direcciones existentes
- **Contadores**: Asignación O(1) para nuevas direcciones
- **Segmentación**: Identificación rápida de tipo por rango

### Escalabilidad
- **Rangos Amplios**: 1000 direcciones por segmento
- **Extensibilidad**: Fácil agregar nuevos tipos
- **Separación**: Segmentos independientes

### Robustez
- **Validación**: Manejo de tipos desconocidos
- **Consistencia**: Misma dirección para mismo elemento
- **Logging**: Advertencias para debugging

### Flexibilidad
- **Tipos Dinámicos**: Inferencia automática cuando es posible
- **Contextos**: Soporte para múltiples funciones
- **Reset**: Capacidad de reiniciar contextos

## Casos de Uso Comunes

### Asignar Variable Global
```java
int address = memory.getVariableAddress("x", "int", true);
// address = 1000 (primera variable global int)
```

### Asignar Constante
```java
int address = memory.getConstantAddress("42", "int");
// address = 11000 (primera constante int)
```

### Crear Temporal
```java
int address = memory.getTempAddressForName("t0", "int");
// address = 7000 (primera temporal int)
```

### Configurar Función con Parámetros
```java
memory.setParameterCount(2);
// localIntCounter ahora empieza en 4002
```

## Ventajas del Diseño

### Organización Clara
- Segmentos bien definidos facilitan debugging
- Rangos permiten identificar tipo de variable por dirección
- Separación clara entre ámbitos

### Gestión Automática
- Asignación automática de direcciones
- Deduplicación de constantes
- Inferencia de tipos cuando es posible

### Integración Sencilla
- Interfaz simple para otros componentes
- Coordinación transparente con sistema de memoria
- Soporte completo para características del lenguaje
