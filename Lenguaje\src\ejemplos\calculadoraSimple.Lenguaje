programa CalculadoraSimple ;
var resultadoActual : float ;
var operacionesRealizadas : int ;

func void sumar (val1 : float, val2 : float) [ {
    resultadoActual = val1 + val2 ;
    operacionesRealizadas = operacionesRealizadas + 1 ;
} ] ;

func void restar (val1 : float, val2 : float) [ 
    var diferencia : float ;
    {
    diferencia = val1 - val2 ;
    resultadoActual = diferencia ;
    operacionesRealizadas = operacionesRealizadas + 1 ;
} ] ;

func void mostrarResultado () [ {
    print ("El resultado actual es ") ;
    print (resultadoActual) ;
    print ("Total de operaciones ") ;
    print (operacionesRealizadas) ;
} ] ;

main {
    resultadoActual = 0.0 ;
    operacionesRealizadas = 0 ;

    sumar (10.5, 5.2) ;
    mostrarResultado () ;

    restar (resultadoActual, 3.0) ;
    mostrarResultado () ;

    print ("") ;
    print ("Finalizando calculadora") ;
} end