token literal names:
null
'programa'
'var'
'int'
'str'
'float'
'void'
'func'
'main'
'print'
'if'
'else'
'while'
'do'
'end'
'='
'+'
'-'
'*'
'/'
'=='
'!='
'>'
'<'
'&&'
'||'
';'
':'
','
'('
')'
'{'
'}'
'['
']'
null
null
null
null
null

token symbolic names:
null
PROGRAM
VAR
INT
STRING
FLOAT
VOID
FUNC
MAIN
PRINT
IF
ELSE
WHILE
DO
END
ASSIGN
PLUS
MINUS
MULT
DIV
EQ
NEQ
GT
LT
AND
OR
SEMI
COLON
COMMA
LPAREN
RPAREN
LBRACE
RBRACE
LBRACK
RBRACK
ID
CTE_INT
CTE_FLOAT
CTE_STRING
WS

rule names:
program
vars_opt
funcs_p
vars
id_list
type
funcs
atributo
atr_opt
body
statement_list
statement
assing
condition
cycle
f_call
expresion_list_opt
expresion_list
print
expresion
andExpr
comparacion
aritmetica
termino
factor
cte


atn:
[4, 1, 39, 237, 2, 0, 7, 0, 2, 1, 7, 1, 2, 2, 7, 2, 2, 3, 7, 3, 2, 4, 7, 4, 2, 5, 7, 5, 2, 6, 7, 6, 2, 7, 7, 7, 2, 8, 7, 8, 2, 9, 7, 9, 2, 10, 7, 10, 2, 11, 7, 11, 2, 12, 7, 12, 2, 13, 7, 13, 2, 14, 7, 14, 2, 15, 7, 15, 2, 16, 7, 16, 2, 17, 7, 17, 2, 18, 7, 18, 2, 19, 7, 19, 2, 20, 7, 20, 2, 21, 7, 21, 2, 22, 7, 22, 2, 23, 7, 23, 2, 24, 7, 24, 2, 25, 7, 25, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 4, 1, 63, 8, 1, 11, 1, 12, 1, 64, 1, 1, 3, 1, 68, 8, 1, 1, 2, 1, 2, 1, 2, 1, 2, 3, 2, 74, 8, 2, 1, 3, 1, 3, 1, 3, 1, 3, 1, 3, 1, 3, 1, 4, 1, 4, 1, 4, 5, 4, 85, 8, 4, 10, 4, 12, 4, 88, 9, 4, 1, 5, 1, 5, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 7, 1, 7, 1, 7, 1, 7, 1, 7, 1, 7, 3, 7, 110, 8, 7, 1, 8, 1, 8, 1, 8, 3, 8, 115, 8, 8, 1, 9, 1, 9, 1, 9, 1, 9, 1, 10, 4, 10, 122, 8, 10, 11, 10, 12, 10, 123, 1, 10, 3, 10, 127, 8, 10, 1, 11, 1, 11, 1, 11, 1, 11, 1, 11, 3, 11, 134, 8, 11, 1, 12, 1, 12, 1, 12, 1, 12, 1, 12, 1, 13, 1, 13, 1, 13, 1, 13, 1, 13, 1, 13, 1, 13, 3, 13, 148, 8, 13, 1, 13, 1, 13, 1, 14, 1, 14, 1, 14, 1, 14, 1, 14, 1, 14, 1, 14, 1, 14, 1, 15, 1, 15, 1, 15, 1, 15, 1, 15, 1, 15, 1, 16, 1, 16, 3, 16, 168, 8, 16, 1, 17, 1, 17, 1, 17, 5, 17, 173, 8, 17, 10, 17, 12, 17, 176, 9, 17, 1, 18, 1, 18, 1, 18, 1, 18, 1, 18, 1, 18, 1, 19, 1, 19, 1, 19, 5, 19, 187, 8, 19, 10, 19, 12, 19, 190, 9, 19, 1, 20, 1, 20, 1, 20, 5, 20, 195, 8, 20, 10, 20, 12, 20, 198, 9, 20, 1, 21, 1, 21, 1, 21, 3, 21, 203, 8, 21, 1, 22, 1, 22, 1, 22, 5, 22, 208, 8, 22, 10, 22, 12, 22, 211, 9, 22, 1, 23, 1, 23, 1, 23, 5, 23, 216, 8, 23, 10, 23, 12, 23, 219, 9, 23, 1, 24, 1, 24, 1, 24, 1, 24, 1, 24, 3, 24, 226, 8, 24, 1, 24, 1, 24, 3, 24, 230, 8, 24, 1, 24, 3, 24, 233, 8, 24, 1, 25, 1, 25, 1, 25, 0, 0, 26, 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 0, 5, 1, 0, 3, 5, 1, 0, 20, 23, 1, 0, 16, 17, 1, 0, 18, 19, 1, 0, 36, 38, 234, 0, 52, 1, 0, 0, 0, 2, 67, 1, 0, 0, 0, 4, 73, 1, 0, 0, 0, 6, 75, 1, 0, 0, 0, 8, 81, 1, 0, 0, 0, 10, 89, 1, 0, 0, 0, 12, 91, 1, 0, 0, 0, 14, 109, 1, 0, 0, 0, 16, 114, 1, 0, 0, 0, 18, 116, 1, 0, 0, 0, 20, 126, 1, 0, 0, 0, 22, 133, 1, 0, 0, 0, 24, 135, 1, 0, 0, 0, 26, 140, 1, 0, 0, 0, 28, 151, 1, 0, 0, 0, 30, 159, 1, 0, 0, 0, 32, 167, 1, 0, 0, 0, 34, 169, 1, 0, 0, 0, 36, 177, 1, 0, 0, 0, 38, 183, 1, 0, 0, 0, 40, 191, 1, 0, 0, 0, 42, 199, 1, 0, 0, 0, 44, 204, 1, 0, 0, 0, 46, 212, 1, 0, 0, 0, 48, 232, 1, 0, 0, 0, 50, 234, 1, 0, 0, 0, 52, 53, 5, 1, 0, 0, 53, 54, 5, 35, 0, 0, 54, 55, 5, 26, 0, 0, 55, 56, 3, 2, 1, 0, 56, 57, 3, 4, 2, 0, 57, 58, 5, 8, 0, 0, 58, 59, 3, 18, 9, 0, 59, 60, 5, 14, 0, 0, 60, 1, 1, 0, 0, 0, 61, 63, 3, 6, 3, 0, 62, 61, 1, 0, 0, 0, 63, 64, 1, 0, 0, 0, 64, 62, 1, 0, 0, 0, 64, 65, 1, 0, 0, 0, 65, 68, 1, 0, 0, 0, 66, 68, 1, 0, 0, 0, 67, 62, 1, 0, 0, 0, 67, 66, 1, 0, 0, 0, 68, 3, 1, 0, 0, 0, 69, 70, 3, 12, 6, 0, 70, 71, 3, 4, 2, 0, 71, 74, 1, 0, 0, 0, 72, 74, 1, 0, 0, 0, 73, 69, 1, 0, 0, 0, 73, 72, 1, 0, 0, 0, 74, 5, 1, 0, 0, 0, 75, 76, 5, 2, 0, 0, 76, 77, 3, 8, 4, 0, 77, 78, 5, 27, 0, 0, 78, 79, 3, 10, 5, 0, 79, 80, 5, 26, 0, 0, 80, 7, 1, 0, 0, 0, 81, 86, 5, 35, 0, 0, 82, 83, 5, 28, 0, 0, 83, 85, 5, 35, 0, 0, 84, 82, 1, 0, 0, 0, 85, 88, 1, 0, 0, 0, 86, 84, 1, 0, 0, 0, 86, 87, 1, 0, 0, 0, 87, 9, 1, 0, 0, 0, 88, 86, 1, 0, 0, 0, 89, 90, 7, 0, 0, 0, 90, 11, 1, 0, 0, 0, 91, 92, 5, 7, 0, 0, 92, 93, 5, 6, 0, 0, 93, 94, 5, 35, 0, 0, 94, 95, 5, 29, 0, 0, 95, 96, 3, 14, 7, 0, 96, 97, 5, 30, 0, 0, 97, 98, 5, 33, 0, 0, 98, 99, 3, 2, 1, 0, 99, 100, 3, 18, 9, 0, 100, 101, 5, 34, 0, 0, 101, 102, 5, 26, 0, 0, 102, 13, 1, 0, 0, 0, 103, 104, 5, 35, 0, 0, 104, 105, 5, 27, 0, 0, 105, 106, 3, 10, 5, 0, 106, 107, 3, 16, 8, 0, 107, 110, 1, 0, 0, 0, 108, 110, 1, 0, 0, 0, 109, 103, 1, 0, 0, 0, 109, 108, 1, 0, 0, 0, 110, 15, 1, 0, 0, 0, 111, 112, 5, 28, 0, 0, 112, 115, 3, 14, 7, 0, 113, 115, 1, 0, 0, 0, 114, 111, 1, 0, 0, 0, 114, 113, 1, 0, 0, 0, 115, 17, 1, 0, 0, 0, 116, 117, 5, 31, 0, 0, 117, 118, 3, 20, 10, 0, 118, 119, 5, 32, 0, 0, 119, 19, 1, 0, 0, 0, 120, 122, 3, 22, 11, 0, 121, 120, 1, 0, 0, 0, 122, 123, 1, 0, 0, 0, 123, 121, 1, 0, 0, 0, 123, 124, 1, 0, 0, 0, 124, 127, 1, 0, 0, 0, 125, 127, 1, 0, 0, 0, 126, 121, 1, 0, 0, 0, 126, 125, 1, 0, 0, 0, 127, 21, 1, 0, 0, 0, 128, 134, 3, 24, 12, 0, 129, 134, 3, 26, 13, 0, 130, 134, 3, 28, 14, 0, 131, 134, 3, 30, 15, 0, 132, 134, 3, 36, 18, 0, 133, 128, 1, 0, 0, 0, 133, 129, 1, 0, 0, 0, 133, 130, 1, 0, 0, 0, 133, 131, 1, 0, 0, 0, 133, 132, 1, 0, 0, 0, 134, 23, 1, 0, 0, 0, 135, 136, 5, 35, 0, 0, 136, 137, 5, 15, 0, 0, 137, 138, 3, 38, 19, 0, 138, 139, 5, 26, 0, 0, 139, 25, 1, 0, 0, 0, 140, 141, 5, 10, 0, 0, 141, 142, 5, 29, 0, 0, 142, 143, 3, 38, 19, 0, 143, 144, 5, 30, 0, 0, 144, 147, 3, 18, 9, 0, 145, 146, 5, 11, 0, 0, 146, 148, 3, 18, 9, 0, 147, 145, 1, 0, 0, 0, 147, 148, 1, 0, 0, 0, 148, 149, 1, 0, 0, 0, 149, 150, 5, 26, 0, 0, 150, 27, 1, 0, 0, 0, 151, 152, 5, 12, 0, 0, 152, 153, 5, 29, 0, 0, 153, 154, 3, 38, 19, 0, 154, 155, 5, 30, 0, 0, 155, 156, 5, 13, 0, 0, 156, 157, 3, 18, 9, 0, 157, 158, 5, 26, 0, 0, 158, 29, 1, 0, 0, 0, 159, 160, 5, 35, 0, 0, 160, 161, 5, 29, 0, 0, 161, 162, 3, 32, 16, 0, 162, 163, 5, 30, 0, 0, 163, 164, 5, 26, 0, 0, 164, 31, 1, 0, 0, 0, 165, 168, 3, 34, 17, 0, 166, 168, 1, 0, 0, 0, 167, 165, 1, 0, 0, 0, 167, 166, 1, 0, 0, 0, 168, 33, 1, 0, 0, 0, 169, 174, 3, 38, 19, 0, 170, 171, 5, 28, 0, 0, 171, 173, 3, 38, 19, 0, 172, 170, 1, 0, 0, 0, 173, 176, 1, 0, 0, 0, 174, 172, 1, 0, 0, 0, 174, 175, 1, 0, 0, 0, 175, 35, 1, 0, 0, 0, 176, 174, 1, 0, 0, 0, 177, 178, 5, 9, 0, 0, 178, 179, 5, 29, 0, 0, 179, 180, 3, 34, 17, 0, 180, 181, 5, 30, 0, 0, 181, 182, 5, 26, 0, 0, 182, 37, 1, 0, 0, 0, 183, 188, 3, 40, 20, 0, 184, 185, 5, 25, 0, 0, 185, 187, 3, 40, 20, 0, 186, 184, 1, 0, 0, 0, 187, 190, 1, 0, 0, 0, 188, 186, 1, 0, 0, 0, 188, 189, 1, 0, 0, 0, 189, 39, 1, 0, 0, 0, 190, 188, 1, 0, 0, 0, 191, 196, 3, 42, 21, 0, 192, 193, 5, 24, 0, 0, 193, 195, 3, 42, 21, 0, 194, 192, 1, 0, 0, 0, 195, 198, 1, 0, 0, 0, 196, 194, 1, 0, 0, 0, 196, 197, 1, 0, 0, 0, 197, 41, 1, 0, 0, 0, 198, 196, 1, 0, 0, 0, 199, 202, 3, 44, 22, 0, 200, 201, 7, 1, 0, 0, 201, 203, 3, 44, 22, 0, 202, 200, 1, 0, 0, 0, 202, 203, 1, 0, 0, 0, 203, 43, 1, 0, 0, 0, 204, 209, 3, 46, 23, 0, 205, 206, 7, 2, 0, 0, 206, 208, 3, 46, 23, 0, 207, 205, 1, 0, 0, 0, 208, 211, 1, 0, 0, 0, 209, 207, 1, 0, 0, 0, 209, 210, 1, 0, 0, 0, 210, 45, 1, 0, 0, 0, 211, 209, 1, 0, 0, 0, 212, 217, 3, 48, 24, 0, 213, 214, 7, 3, 0, 0, 214, 216, 3, 48, 24, 0, 215, 213, 1, 0, 0, 0, 216, 219, 1, 0, 0, 0, 217, 215, 1, 0, 0, 0, 217, 218, 1, 0, 0, 0, 218, 47, 1, 0, 0, 0, 219, 217, 1, 0, 0, 0, 220, 221, 5, 29, 0, 0, 221, 222, 3, 38, 19, 0, 222, 223, 5, 30, 0, 0, 223, 233, 1, 0, 0, 0, 224, 226, 7, 2, 0, 0, 225, 224, 1, 0, 0, 0, 225, 226, 1, 0, 0, 0, 226, 227, 1, 0, 0, 0, 227, 233, 5, 35, 0, 0, 228, 230, 7, 2, 0, 0, 229, 228, 1, 0, 0, 0, 229, 230, 1, 0, 0, 0, 230, 231, 1, 0, 0, 0, 231, 233, 3, 50, 25, 0, 232, 220, 1, 0, 0, 0, 232, 225, 1, 0, 0, 0, 232, 229, 1, 0, 0, 0, 233, 49, 1, 0, 0, 0, 234, 235, 7, 4, 0, 0, 235, 51, 1, 0, 0, 0, 20, 64, 67, 73, 86, 109, 114, 123, 126, 133, 147, 167, 174, 188, 196, 202, 209, 217, 225, 229, 232]