programa AnidacionProfunda ;
var nivelGlobal : int ;
var esPar : int ;

func void procesarNumero (valor : int) [
    var temporal : int ;
    {
    temporal = valor ;
    if (temporal / 2 * 2 == temporal) {
        esPar = 1 ;
        print ("El ") ;
        print (temporal) ;
        print (" es par") ;
    } else {
        esPar = 0 ;
        print ("El ") ;
        print (temporal) ;
        print (" es impar") ;
    } ;
    nivelGlobal = nivelGlobal + 1 ;
} ] ;

func void cicloAnidado (veces : int) [
    var i, j : int ;
    {
    i = 0 ;
    while (i < veces) do {
        print ("Ciclo externo iteracion ") ;
        print (i) ;
        
        j = 0 ;
        while (j < 2) do {
            print ("  Ciclo interno iteracion ") ;
            print (j) ;
            j = j + 1 ;
        } ;
        
        if (i == 1) {
            print ("  Condicion especial en iteracion 1") ;
        } else {
            print ("  Iteracion normal") ;
        } ;
        
        i = i + 1 ;
    } ;
    print ("Fin de ciclos anidados") ;
} ] ;

main {
    nivelGlobal = 0 ;
    esPar = 0 ;

    print ("Inicio de Anidacion") ;

    procesarNumero (7) ;
    print ("") ;

    procesarNumero (12) ;

    print ("") ;
    print ("Iniciando ejemplo de ciclos anidados") ;
    cicloAnidado (3) ;

    print ("") ;
    print ("Nivel global final ") ;
    print (nivelGlobal) ;
    
    if (nivelGlobal > 2 && esPar == 1) {
        print ("") ;
        print ("Condicion final cumplida y ultimo fue par") ;
    } else {
        print ("") ;
        print ("Condicion final no cumplida o ultimo fue impar") ;
    } ;

    print ("Fin de Anidacion") ;
} end