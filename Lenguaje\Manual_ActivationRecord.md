# Manual de ActivationRecord.java

## Propósito General
La clase `ActivationRecord` representa un Registro de Activación en la Máquina Virtual. Su función principal es manejar el contexto de ejecución de una función, incluyendo parámetros, variables locales, y la dirección de retorno. Cada instancia representa el estado completo de una llamada a función.

## Concepto de Registro de Activación
Un Registro de Activación es una estructura de datos que se crea cada vez que se llama a una función y contiene:
- **Parámetros** de la función
- **Variables locales** declaradas en la función
- **Dirección de retorno** (dónde continuar después de la función)
- **Dirección base** para el cálculo de direcciones locales

## Estructura de Datos

### Variables de Instancia
```java
private int returnAddress;                    // Dirección de retorno
private Map<Integer, Object> localMemory;     // Memoria local de la función
private String functionName;                  // Nombre de la función
private int baseAddress;                      // Dirección base para variables locales
```

### Detalles de las Variables

#### returnAddress (int)
- **Propósito**: Almacena el índice del cuádruplo al que debe regresar la ejecución
- **Uso**: Cuando la función termina, la VM continúa desde esta dirección
- **Valor**: Índice en la lista de cuádruplos (programCounter + 1)

#### localMemory (Map<Integer, Object>)
- **Propósito**: Almacena todas las variables locales y parámetros de la función
- **Estructura**: HashMap donde la clave es la dirección virtual y el valor es el dato
- **Rango**: Direcciones locales (4000-6999) según VirtualMemory

#### functionName (String)
- **Propósito**: Identificador de la función para debugging y gestión
- **Formato**: Nombre original de la función o hash generado
- **Uso**: Útil para trazas de ejecución y debugging

#### baseAddress (int)
- **Propósito**: Dirección base para calcular direcciones relativas de variables locales
- **Cálculo**: LOCAL_INT_START + (nivel_de_anidamiento * 1000)
- **Uso**: Permite que cada función tenga su propio espacio de direcciones

## Métodos Principales

### Constructor
```java
public ActivationRecord(String functionName, int returnAddress, int baseAddress)
```
**Parámetros**:
- `functionName`: Nombre de la función
- `returnAddress`: Dirección de retorno (-1 si no se ha establecido)
- `baseAddress`: Dirección base para variables locales

**Inicialización**:
- Crea un HashMap vacío para `localMemory`
- Establece los valores proporcionados
- Prepara el registro para uso inmediato

### Gestión de Memoria Local

#### getValue(int address)
**Propósito**: Recuperar un valor de la memoria local

**Flujo**:
1. Consulta el HashMap `localMemory` con la dirección
2. Retorna el valor almacenado o `null` si no existe
3. No valida rangos (responsabilidad de VirtualMachine)

#### setValue(int address, Object value)
**Propósito**: Almacenar un valor en la memoria local

**Flujo**:
1. Almacena el valor en `localMemory` usando la dirección como clave
2. Sobrescribe valores existentes sin validación
3. Acepta cualquier tipo de Object

### Gestión de Parámetros

#### setParameter(int paramIndex, Object value)
**Propósito**: Establecer un parámetro de la función

**Cálculo de Dirección**:
```java
int paramAddress = baseAddress + paramIndex;
```

**Flujo**:
1. Calcula la dirección del parámetro basándose en el índice
2. Almacena el valor en `localMemory`
3. Los parámetros se almacenan en direcciones consecutivas

**Ejemplo**:
- baseAddress = 4000
- Parámetro 0 → dirección 4000
- Parámetro 1 → dirección 4001
- Parámetro 2 → dirección 4002

#### getParameter(int paramIndex)
**Propósito**: Recuperar un parámetro por su índice

**Flujo**:
1. Calcula la dirección del parámetro
2. Consulta `localMemory` con esa dirección
3. Retorna el valor del parámetro

### Gestión de Dirección de Retorno

#### getReturnAddress()
- Retorna la dirección de retorno almacenada
- Usado por VirtualMachine para continuar ejecución

#### setReturnAddress(int address)
- Establece la dirección de retorno
- Llamado durante GOSUB para establecer dónde regresar

### Métodos de Información

#### getFunctionName()
- Retorna el nombre de la función
- Útil para debugging y trazas

#### getBaseAddress()
- Retorna la dirección base
- Usado para cálculos de direcciones relativas

#### getLocalMemory()
- Retorna una copia de la memoria local
- Previene modificaciones accidentales
- Útil para debugging

### Métodos de Utilidad

#### clearLocalMemory()
**Propósito**: Limpiar toda la memoria local

**Uso**:
- Llamado al finalizar la ejecución de la función
- Libera memoria y previene referencias obsoletas
- Prepara el registro para reutilización

#### printState()
**Propósito**: Imprimir el estado completo del registro para debugging

**Información Mostrada**:
- Nombre de la función
- Dirección de retorno
- Dirección base
- Contenido completo de la memoria local

## Interacción con Otros Componentes

### Con VirtualMachine
- **Creación**: VirtualMachine crea ActivationRecord durante ERA
- **Gestión**: VirtualMachine maneja la pila de registros (callStack)
- **Acceso**: VirtualMachine usa getValue/setValue para variables locales
- **Limpieza**: VirtualMachine limpia registros al terminar funciones

### Con ExecutionMemory
- **Separación**: ActivationRecord maneja memoria local, ExecutionMemory el resto
- **Coordinación**: VirtualMachine decide cuándo usar cada uno
- **Rangos**: ActivationRecord maneja direcciones 4000-6999

### Con GeneradorCuadruplos
- **Direcciones**: GeneradorCuadruplos genera direcciones que ActivationRecord usa
- **Parámetros**: Las direcciones de parámetros son calculadas por ActivationRecord

## Flujo de Ejecución en Llamadas a Función

### Durante ERA (Espacio de Registro de Activación)
1. VirtualMachine crea un nuevo ActivationRecord
2. Se establece el nombre de la función y dirección base
3. La dirección de retorno se establece como -1 (pendiente)
4. El registro queda listo para recibir parámetros

### Durante PARAM (Paso de Parámetros)
1. VirtualMachine obtiene el valor del parámetro
2. Llama a `setParameter()` con el índice correspondiente
3. El parámetro se almacena en la dirección calculada
4. Se repite para todos los parámetros

### Durante GOSUB (Llamada a Función)
1. VirtualMachine establece la dirección de retorno con `setReturnAddress()`
2. El registro se empuja a la pila de llamadas (callStack)
3. La ejecución salta a la función

### Durante Ejecución de la Función
1. Variables locales se almacenan con `setValue()`
2. Parámetros se acceden con `getValue()`
3. Todas las operaciones locales usan este registro

### Durante RETURN (Retorno de Función)
1. VirtualMachine obtiene la dirección de retorno con `getReturnAddress()`
2. Se limpia la memoria local con `clearLocalMemory()`
3. El registro se saca de la pila de llamadas
4. La ejecución continúa en la dirección de retorno

## Casos de Uso Comunes

### Crear Registro para Función
```java
ActivationRecord record = new ActivationRecord("miFuncion", -1, 4000);
```

### Establecer Parámetros
```java
record.setParameter(0, 42);      // Primer parámetro
record.setParameter(1, "hello"); // Segundo parámetro
```

### Acceder a Variable Local
```java
Object valor = record.getValue(4010);  // Variable local en dirección 4010
```

### Limpiar al Terminar
```java
record.clearLocalMemory();  // Liberar memoria local
```

## Consideraciones de Diseño

### Eficiencia
- HashMap para acceso O(1) a variables locales
- Cálculo directo de direcciones de parámetros
- Limpieza explícita de memoria

### Flexibilidad
- Acepta cualquier tipo de Object como valor
- Direcciones base configurables
- Nombres de función para identificación

### Debugging
- Método `printState()` para inspección completa
- Acceso a copia de memoria local
- Información completa del contexto de función
