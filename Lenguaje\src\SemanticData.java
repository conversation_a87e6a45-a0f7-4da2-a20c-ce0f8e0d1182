import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

public class SemanticData {
    // Nota: Las variables de instancia anteriores no se usaban en el código
    // Se mantienen solo las clases estáticas y métodos necesarios

    public static class Variable {
        public String nombre;
        public String tipo;
        // Puedes agregar más atributos como scope, dirección, etc.

        public Variable(String nombre, String tipo) {
            this.nombre = nombre;
            this.tipo = tipo;
        }
    }

    public static class Funcion {
        public String nombre;
        public String tipoRetorno;
        public List<Variable> parametros;
        public Map<String, Variable> tablaVariablesLocal;
        // Puedes agregar más atributos como dirección de inicio, etc.

        public Funcion(String nombre, String tipoRetorno) {
            this.nombre = nombre;
            this.tipoRetorno = tipoRetorno;
            this.parametros = new ArrayList<>();
            this.tablaVariablesLocal = new HashMap<>();
        }
    }

    public static Map<String, Funcion> directorioFunciones = new HashMap<>();
    public static Map<String, Variable> tablaVariablesGlobal = new HashMap<>();
    public static Stack<Map<String, Variable>> pilaTablasVariables = new Stack<>();

    static {
        pilaTablasVariables.push(tablaVariablesGlobal);
    }

    public static Map<String, Map<String, Map<String, String>>> cuboSemantico = new HashMap<>();

    static {
        // Inicialización del cubo semántico usando HashMap en lugar de Map.of()
        Map<String, Map<String, String>> intOps = new HashMap<>();

        Map<String, String> intPlus = new HashMap<>();
        intPlus.put("int", "int");
        intPlus.put("float", "float");
        intOps.put("+", intPlus);

        Map<String, String> intMinus = new HashMap<>();
        intMinus.put("int", "int");
        intMinus.put("float", "float");
        intOps.put("-", intMinus);

        Map<String, String> intMult = new HashMap<>();
        intMult.put("int", "int");
        intMult.put("float", "float");
        intOps.put("*", intMult);

        Map<String, String> intDiv = new HashMap<>();
        intDiv.put("int", "float");
        intDiv.put("float", "float");
        intOps.put("/", intDiv);

        Map<String, String> intEq = new HashMap<>();
        intEq.put("int", "bool");
        intEq.put("float", "bool");
        intOps.put("==", intEq);

        Map<String, String> intNeq = new HashMap<>();
        intNeq.put("int", "bool");
        intNeq.put("float", "bool");
        intOps.put("!=", intNeq);

        Map<String, String> intGt = new HashMap<>();
        intGt.put("int", "bool");
        intGt.put("float", "bool");
        intOps.put(">", intGt);

        Map<String, String> intLt = new HashMap<>();
        intLt.put("int", "bool");
        intLt.put("float", "bool");
        intOps.put("<", intLt);

        Map<String, String> intAssign = new HashMap<>();
        intAssign.put("int", "int");
        intAssign.put("float", "float");
        intOps.put("=", intAssign);

        cuboSemantico.put("int", intOps);

        // Float operations
        Map<String, Map<String, String>> floatOps = new HashMap<>();

        Map<String, String> floatPlus = new HashMap<>();
        floatPlus.put("int", "float");
        floatPlus.put("float", "float");
        floatOps.put("+", floatPlus);

        Map<String, String> floatMinus = new HashMap<>();
        floatMinus.put("int", "float");
        floatMinus.put("float", "float");
        floatOps.put("-", floatMinus);

        Map<String, String> floatMult = new HashMap<>();
        floatMult.put("int", "float");
        floatMult.put("float", "float");
        floatOps.put("*", floatMult);

        Map<String, String> floatDiv = new HashMap<>();
        floatDiv.put("int", "float");
        floatDiv.put("float", "float");
        floatOps.put("/", floatDiv);

        Map<String, String> floatEq = new HashMap<>();
        floatEq.put("int", "bool");
        floatEq.put("float", "bool");
        floatOps.put("==", floatEq);

        Map<String, String> floatNeq = new HashMap<>();
        floatNeq.put("int", "bool");
        floatNeq.put("float", "bool");
        floatOps.put("!=", floatNeq);

        Map<String, String> floatGt = new HashMap<>();
        floatGt.put("int", "bool");
        floatGt.put("float", "bool");
        floatOps.put(">", floatGt);

        Map<String, String> floatLt = new HashMap<>();
        floatLt.put("int", "bool");
        floatLt.put("float", "bool");
        floatOps.put("<", floatLt);

        Map<String, String> floatAssign = new HashMap<>();
        floatAssign.put("float", "float");
        floatOps.put("=", floatAssign);

        cuboSemantico.put("float", floatOps);

        // String operations
        Map<String, Map<String, String>> strOps = new HashMap<>();

        Map<String, String> strPlus = new HashMap<>();
        strPlus.put("str", "str");
        strOps.put("+", strPlus);

        Map<String, String> strEq = new HashMap<>();
        strEq.put("str", "bool");
        strOps.put("==", strEq);

        Map<String, String> strNeq = new HashMap<>();
        strNeq.put("str", "bool");
        strOps.put("!=", strNeq);

        Map<String, String> strAssign = new HashMap<>();
        strAssign.put("str", "str");
        strOps.put("=", strAssign);

        cuboSemantico.put("str", strOps);
    }

    public static final List<String> tiposValidos = java.util.Arrays.asList("int", "float", "str");
}