PROGRAM=1
VAR=2
INT=3
STRING=4
FLOAT=5
VOID=6
FUNC=7
MAIN=8
PRINT=9
IF=10
ELSE=11
WHILE=12
DO=13
END=14
ASSIGN=15
PLUS=16
MINUS=17
MULT=18
DIV=19
EQ=20
NEQ=21
GT=22
LT=23
AND=24
OR=25
SEMI=26
COLON=27
COMMA=28
LPAREN=29
RPAREN=30
LBRACE=31
RBRACE=32
LBRACK=33
RBRACK=34
ID=35
CTE_INT=36
CTE_FLOAT=37
CTE_STRING=38
WS=39
'programa'=1
'var'=2
'int'=3
'str'=4
'float'=5
'void'=6
'func'=7
'main'=8
'print'=9
'if'=10
'else'=11
'while'=12
'do'=13
'end'=14
'='=15
'+'=16
'-'=17
'*'=18
'/'=19
'=='=20
'!='=21
'>'=22
'<'=23
'&&'=24
'||'=25
';'=26
':'=27
','=28
'('=29
')'=30
'{'=31
'}'=32
'['=33
']'=34
