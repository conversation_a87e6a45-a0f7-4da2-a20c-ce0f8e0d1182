import java.util.HashMap;
import java.util.Map;

/**
 * Clase que maneja la memoria de ejecución de la Máquina Virtual.
 * Gestiona las diferentes segmentos de memoria: globales, locales, temporales y constantes.
 */
public class ExecutionMemory {

    // Mapas para cada segmento de memoria
    private Map<Integer, Object> globalMemory;      // Variables globales
    private Map<Integer, Object> constantMemory;    // Constantes
    private Map<Integer, Object> tempMemory;        // Variables temporales

    /**
     * Constructor que inicializa todos los segmentos de memoria.
     */
    public ExecutionMemory() {
        this.globalMemory = new HashMap<>();
        this.constantMemory = new HashMap<>();
        this.tempMemory = new HashMap<>();
    }

    /**
     * Obtiene un valor de memoria basado en la dirección virtual.
     * @param address Dirección virtual
     * @return Valor almacenado en esa dirección
     */
    public Object getValue(int address) {
        // Determinar el segmento de memoria basado en la dirección
        if (address >= VirtualMemory.GLOBAL_INT_START && address < VirtualMemory.LOCAL_INT_START) {
            // Memoria global
            return globalMemory.get(address);
        } else if (address >= VirtualMemory.LOCAL_INT_START && address < VirtualMemory.TEMP_INT_START) {
            // Memoria local - esto debería ser manejado por VirtualMachine, no aquí
            // Retornamos null para que VirtualMachine use el ActivationRecord
            return null;
        } else if (address >= VirtualMemory.TEMP_INT_START && address < VirtualMemory.CTE_INT_START) {
            // Memoria temporal
            return tempMemory.get(address);
        } else if (address >= VirtualMemory.CTE_INT_START) {
            // Memoria de constantes
            return constantMemory.get(address);
        } else {
            throw new RuntimeException("Dirección de memoria inválida: " + address);
        }
    }

    /**
     * Establece un valor en memoria basado en la dirección virtual.
     * @param address Dirección virtual
     * @param value Valor a almacenar
     */
    public void setValue(int address, Object value) {
        // Determinar el segmento de memoria basado en la dirección
        if (address >= VirtualMemory.GLOBAL_INT_START && address < VirtualMemory.LOCAL_INT_START) {
            // Memoria global
            globalMemory.put(address, value);
        } else if (address >= VirtualMemory.LOCAL_INT_START && address < VirtualMemory.TEMP_INT_START) {
            // Memoria local - esto debería ser manejado por VirtualMachine, no aquí
            throw new RuntimeException("Las direcciones locales deben ser manejadas por ActivationRecord: " + address);
        } else if (address >= VirtualMemory.TEMP_INT_START && address < VirtualMemory.CTE_INT_START) {
            // Memoria temporal
            tempMemory.put(address, value);
        } else if (address >= VirtualMemory.CTE_INT_START) {
            // Memoria de constantes
            constantMemory.put(address, value);
        } else {
            throw new RuntimeException("Dirección de memoria inválida para escritura: " + address);
        }
    }

    /**
     * Inicializa una constante en memoria.
     * @param address Dirección virtual de la constante
     * @param value Valor de la constante
     */
    public void initializeConstant(int address, Object value) {
        constantMemory.put(address, value);
    }

    /**
     * Obtiene el estado actual de la memoria global.
     * @return Mapa con las variables globales
     */
    public Map<Integer, Object> getGlobalMemory() {
        return new HashMap<>(globalMemory);
    }

    /**
     * Obtiene el estado actual de la memoria temporal.
     * @return Mapa con las variables temporales
     */
    public Map<Integer, Object> getTempMemory() {
        return new HashMap<>(tempMemory);
    }

    /**
     * Obtiene el estado actual de la memoria de constantes.
     * @return Mapa con las constantes
     */
    public Map<Integer, Object> getConstantMemory() {
        return new HashMap<>(constantMemory);
    }

    /**
     * Verifica si una dirección de memoria está inicializada.
     * @param address Dirección virtual a verificar
     * @return true si está inicializada, false en caso contrario
     */
    public boolean isInitialized(int address) {
        Object value = getValue(address);
        return value != null;
    }

    /**
     * Imprime el estado actual de toda la memoria para depuración.
     */
    public void printMemoryState() {
        System.out.println("=== Estado de la Memoria ===");

        System.out.println("Memoria Global:");
        for (Map.Entry<Integer, Object> entry : globalMemory.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        System.out.println("Memoria Temporal:");
        for (Map.Entry<Integer, Object> entry : tempMemory.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        System.out.println("Memoria de Constantes:");
        for (Map.Entry<Integer, Object> entry : constantMemory.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        System.out.println("============================");
    }
}