1.  // Punto 1: Procesamiento de un factor (ID o cte)
    -   Si el factor es un ID:
        -   operandStack.push(id.nombre)
        -   typeStack.push(id.tipo) 
    -   Si el factor es una cte:
        -   operandStack.push(cte.valor)
        -   typeStack.push(cte.tipo)

2.  // Punto 2: Operador Multiplicativo (*, /)
    -   Si el operador actual es '*' o '/':
        -   operatorStack.push(operador)

3.  // Punto 1: Procesamiento de un factor (ID o cte)
    -   (Repetir paso 1)

4.  // Generar cuádruplo para *, /
    -   Si operatorStack.top() es '*' o '/':
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
            -   resultado = generarVariableTemporal()
            -   agregarCuadruplo(operador, operando1, operando2, resultado)
            -   operandStack.push(resultado)
            -   typeStack.push(tipoResultado)
        -   Sino:
            -   ERROR("Error de tipo")

5.  // Punto 3: Operador Aditivo (+, -)
    -   Si el operador actual es '+' o '-':
        -   operatorStack.push(operador)

6.  // Generar cuádruplo para +, -
    -   Si operatorStack.top() es '+' o '-':
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
            -   resultado = generarVariableTemporal()
            -   agregarCuadruplo(operador, operando1, operando2, resultado)
            -   operandStack.push(resultado)
            -   typeStack.push(tipoResultado)
        -   Sino:
            -   ERROR("Error de tipo")

7.  // Punto 4: Operador de Comparación (<, >, ==, !=)
    -    Si el operador actual es de comparación:
         -    operatorStack.push(operador)

8.  // Generar cuádruplo para comparacion
    -   Si operatorStack.top() es un operador de comparación:
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

9.  // Punto 5: Operador AND (&&)
    -   Si el operador actual es '&&':
        -    operatorStack.push("&&")

10.  // Generar cuádruplo para AND
     -  Si operatorStack.top() es "&&":
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

11. // Punto 6: Operador OR (||)
    -   Si el operador actual es '||':
        -   operatorStack.push("||")

12.  // Generar cuádruplo para OR
     -  Si operatorStack.top() es "||":
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

13.  // Punto 7: Fin de la asignación
    -   resultadoExpresion = operandStack.pop()
    -   agregarCuadruplo("=", resultadoExpresion, "", variableAsignacion)
