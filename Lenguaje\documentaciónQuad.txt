
        
Desarrollo de aplicaciones avanzadas de ciencias computacionales
Grupo 505

Compilador


Profesor - Elda Guadalupe Quiroga González



Alumnos
Ira-Hy Barrueta De la Mora - A00835435






02 de junio de 2025
Índice
Arquitectura del compilador	4
Componentes del Sistema y sus Relaciones	5
Tokens	8
Expresiones regulares	9
Guia para creación del Lexer y Parsec	13
Semántica	16
Campos:	16
● pilaTablasVariables: Stack<Map<String, SemanticData.Variable>>	16
● directorioFunciones: Map<String, SemanticData.Funcion>	17
● cuboSemantico: Map<String, Map<String, Map<String, String>>>	17
● tiposValidos: List<String>	18
Métodos Privados:	18
● obtenerTipoOperacion(String tipoIzq, String operador, String tipoDer): String	18
● esTipoCompatible(String tipoEsperado, String tipoRecibido): boolean	18
● buscarVariable(String nombre): SemanticData.Variable	19
● obtenerUltimaFuncionDefinida(): String	19
● obtenerTiposExpresionListOpt(LenguajeParser.Expresion_list_optContext ctx): List<String>	19
● obtenerTiposExpresionList(LenguajeParser.Expresion_listContext ctx): List<String>	19
Métodos Sobrescritos de LenguajeBaseVisitor (para visitar nodos del árbol de sintaxis):	19
● visitProgram(LenguajeParser.ProgramContext ctx): String	19
● visitVars_opt(LenguajeParser.Vars_optContext ctx): String	19
● visitFuncs_p(LenguajeParser.Funcs_pContext ctx): String	19
● visitVars(LenguajeParser.VarsContext ctx): String	19
● visitType(LenguajeParser.TypeContext ctx): String	19
● visitFuncs(LenguajeParser.FuncsContext ctx): String	20
● visitAtributo(LenguajeParser.AtributoContext ctx): String	20
● visitAtr_opt(LenguajeParser.Atr_optContext ctx): String	20
● visitBody(LenguajeParser.BodyContext ctx): String	20
● visitStatement_list(LenguajeParser.Statement_listContext ctx): String	20
● visitStatement(LenguajeParser.StatementContext ctx): String	20
● visitAssing(LenguajeParser.AssingContext ctx): String	20
● visitCondition(LenguajeParser.ConditionContext ctx): String	20
● visitCycle(LenguajeParser.CycleContext ctx): String	20
● visitF_call(LenguajeParser.F_callContext ctx): String	20
● visitExpresion_list_opt(LenguajeParser.Expresion_list_optContext ctx): String	21
● visitExpresion_list(LenguajeParser.Expresion_listContext ctx): String	21
● visitPrint(LenguajeParser.PrintContext ctx): String	21
● visitExpresion(LenguajeParser.ExpresionContext ctx): String	21
● visitComparacion(LenguajeParser.ComparacionContext ctx): String	21
● visitAritmetica(LenguajeParser.AritmeticaContext ctx): String	21
● visitTermino(LenguajeParser.TerminoContext ctx): String	21
● visitFactor(LenguajeParser.FactorContext ctx): String	21
● visitCte(LenguajeParser.CteContext ctx): String	21
Cubo Semántico	22
Código de Expresiones y Estatutos lineales (Cuádruplos)	24
Segmentación de memoria	27
Manejo de funciones	28
Máquina Virtual	32
ExecutionMemory	32
VirtualMemory	32
ActivationRecord	33
callStack	33
parameterStack	33
functionAddress	33

Arquitectura del compilador
El compilador sigue un flujo de procesamiento en 4 fases principales:
Código Fuente → Análisis Léxico/Sintáctico → Análisis Semántico → Generación de Código → Ejecución
Fase 1: Análisis Léxico y Sintáctico
Herramienta: ANTLR 4 con gramática Lenguaje.g4
Entrada: Archivo de código fuente (.Lenguaje)
Salida: Árbol de Sintaxis Abstracta (AST)
Función: Verifica que el código respete la sintaxis del lenguaje
Fase 2: Análisis Semántico
Componente: SemanticVisitor.java
Entrada: AST generado por ANTLR
Salida: AST validado + Tabla de símbolos
Función: Verifica la correctitud semántica (tipos, declaraciones, ámbitos)
Fase 3: Generación de Código Intermedio
Componente: GeneradorCuadruplosVisitor.java
Entrada: AST validado + Tabla de símbolos
Salida: Lista de cuádruplos (código intermedio)
Función: Traduce el AST a representación intermedia ejecutable
Fase 4: Ejecución
Componente: VirtualMachine.java
Entrada: Lista de cuádruplos + Valores de constantes
Salida: Resultados del programa
Función: Ejecuta el código intermedio simulando una máquina virtual

Componentes del Sistema y sus Relaciones
1. SemanticData.java - Centro de Información Semántica
Propósito: Almacena toda la información semántica del lenguaje
Cubo Semántico: Define qué operaciones son válidas entre tipos
Tabla de Símbolos: Estructura para variables y funciones
Tipos Válidos: Lista de tipos de datos soportados
Relación con otros componentes:
SemanticVisitor consulta el cubo semántico para validar operaciones
GeneradorCuadruplos usa las tablas de símbolos para generar direcciones
2. VirtualMemory.java - Administrador de Direcciones Virtuales
Propósito: Gestiona la asignación de direcciones virtuales para variables, temporales y constantes
Segmentación: Organiza memoria en segmentos (global, local, temporal, constantes)
Asignación: Proporciona direcciones únicas para cada elemento
Gestión de Ámbitos: Maneja direcciones para diferentes contextos de función
Relación con otros componentes:
GeneradorCuadruplos solicita direcciones para variables y temporales
ExecutionMemory usa los rangos definidos para determinar tipos de memoria
ActivationRecord maneja direcciones locales basándose en estos rangos
3. SemanticVisitor.java - Validador Semántico
Propósito: Recorre el AST validando la correctitud semántica del programa
Validación de Tipos: Verifica compatibilidad en operaciones y asignaciones
Gestión de Ámbitos: Maneja visibilidad de variables (global vs local)
Validación de Funciones: Verifica declaraciones y llamadas a función
Relación con otros componentes:
Recibe AST de ANTLR
Consulta SemanticData para validaciones
Proporciona tabla de símbolos validada a GeneradorCuadruplos
4. GeneradorCuadruplosVisitor.java - Generador de Código Intermedio
Propósito: Traduce el AST a cuádruplos (código intermedio)
Generación de Cuádruplos: Crea instrucciones de 4 componentes (operador, arg1, arg2, resultado)
Gestión de Temporales: Crea variables temporales para resultados intermedios
Control de Flujo: Genera saltos para condicionales y ciclos
Llamadas a Función: Maneja ERA, PARAM, GOSUB, ENDFUNC
Relación con otros componentes:
Recibe AST validado de SemanticVisitor
Usa VirtualMemory para asignar direcciones
Proporciona cuádruplos y constantes a VirtualMachine
5. VirtualMachine.java - Máquina Virtual Ejecutora
Propósito: Ejecuta los cuádruplos simulando una máquina virtual
Interpretación: Ejecuta cuádruplos uno por uno
Gestión de Memoria: Coordina ExecutionMemory y ActivationRecord
Control de Flujo: Maneja saltos, llamadas y retornos de función
Operaciones: Ejecuta operaciones aritméticas, lógicas y de E/S
Relación con otros componentes:
Recibe cuádruplos de GeneradorCuadruplos
Usa ExecutionMemory para memoria global/temporal/constantes
Usa ActivationRecord para memoria local de funciones
6. ExecutionMemory.java - Gestor de Memoria Global
Propósito: Administra memoria global, temporal y de constantes durante ejecución
Segmentación: Maneja diferentes tipos de memoria por separado
Acceso Unificado: Proporciona interfaz única para acceso a memoria
Validación: Verifica rangos de direcciones válidas
Relación con otros componentes:
Usado por VirtualMachine para memoria no-local
Coordina con ActivationRecord para separar responsabilidades
Usa rangos definidos en VirtualMemory
7. ActivationRecord.java - Contexto de Función
Propósito: Maneja el contexto de ejecución de una función individual
Memoria Local: Almacena parámetros y variables locales
Dirección de Retorno: Mantiene dónde continuar después de la función
Aislamiento: Cada función tiene su propio espacio de memoria
Relación con otros componentes:
Usado por VirtualMachine para memoria local de funciones
Coordina con ExecutionMemory para separar ámbitos
Maneja direcciones asignadas por VirtualMemory

Tokens
Palabras Reservadas
program
var
int
float
void
func
main
print
if
else
while
do
end
Identificadores
id (para nombre de variables y funciones)
Constantes
cte.int
cte.string
cte.float
Operadores
= (asignación)
+ (suma)
- (resta)
* (multiplicación)
/ (división)
== (igualdad)
!= (desigualdad)
Símbolos especiales
; (punto y coma)
: (dos puntos)
, (coma)
( (paréntesis de apertura)
) (paréntesis de cierre)
{ (llave de apertura)
} (llave de cierre)
[ (corchete de apertura)
] (corchete de cierre)
Expresiones regulares
Palabras Reservadas
program	ー>	programa
var		ー>	var
int		ー>	int
float		ー>	float
void		ー>	void
func		ー>	func
main		ー>	main
print		ー>	print
if		ー>	if
else		ー>	else
while		ー>	while
do		ー>	do
end		ー>	end
Identificadores
id =  (l1 | l2) ( l1 | l2 | n | _)*
Constantes
cte.int = n+
cte.string = " (l1 | l2 | n | _ )* "
cte.float = (n+.n*) | (n*.n+)
Operadores
= 	ー>	=
+ 	ー>	+
- 	ー>	-
* 	ー>	*
/ 	ー>	/
== 	ー>	=·=
!= 	ー>	 !·=
Símbolos especiales
;	ー>	;
: 	ー>	:
, 	ー>	,
( 	ー>	(
) 	ー>	)
{ 	ー>	{
}	ー>	}
[	ー>	[
]	ー>	]

Gramatical formal
<program> -> PROGRAM ID SEMI <vars_p> <funcs_p> MAIN <body> END
<vars_p> -> <vars> <vars_p> | ε
<funcs_p> -> <funcs> <funcs_p> | ε
<vars> -> VAR <id_list> COLON <type> SEMI
<id_list> -> ID | ID COMMA <id_list>
<type> -> INT | FLOAT | STRING
<funcs> -> FUNC VOID ID LPAREN <atributo> RPAREN LBRACK <vars_p> <body> RBRACK SEMI
<atributo> -> ID COLON <type> <atr_opt> | ε
<atr_opt> -> COMMA <atributo> | ε
<body> -> LBRACE <statement_list> RBRACE
<statement_list> -> <statement> <statement_list> | ε
<statement> -> <assing> | <condition> | <cycle> | <f_call> | <print>
<assing> -> ID ASSIGN <expresion> SEMI
<condition> -> IF LPAREN <expresion> RPAREN <body> SEMI | IF LPAREN <expresion> RPAREN <body> ELSE <body> SEMI
<cycle> -> WHILE LPAREN <expresion> RPAREN DO <body> SEMI
<f_call> -> ID LPAREN <expresion_list_opt> RPAREN SEMI
<expresion_list_opt> -> <expresion_list> | ε
<expresion_list> -> <expresion> | <expresion> COMMA <expresion_list>
<print> -> PRINT LPAREN <print_exp_list> RPAREN SEMI
<print_exp_list> -> <expresion> | <expresion> COMMA <print_exp_list> | CTE_STRING | CTE_STRING COMMA <print_exp_list>
<expresion> -> <andExpr> | <andExpr> OR <expresion>
<andExpr> -> <comparacion> | <comparacion> AND <andExpr>
<comparacion> -> <aritmetica> | <aritmetica> GT <aritmetica> | <aritmetica> LT <aritmetica> | <aritmetica> NEQ <aritmetica> | <aritmetica> EQ <aritmetica>
<aritmetica> -> <termino> | <termino> PLUS <aritmetica> | <termino> MINUS <aritmetica>
<termino> -> <factor> | <factor> MULT <termino> | <factor> DIV <termino>
<factor> -> LPAREN <expresion> RPAREN | PLUS ID | MINUS ID | PLUS <cte> | MINUS <cte> | ID | <cte>
<cte> -> CTE_INT | CTE_FLOAT | CTE_STRING


Guia para creación del Lexer y Parsec
Descargar la versión más reciente de Java SE Development Kit
https://www.oracle.com/java/technologies/downloads/#jdk24-windows
Descargar antlr-Versión_mas_reciente-complete.jar y colocarlo en una carpeta C:\Javalib 
https://www-antlr-org.translate.goog/download.html?_x_tr_sl=en&_x_tr_tl=es&_x_tr_hl=es&_x_tr_pto=tc
Añadir antlr-Versión_mas_reciente-complete.jar al Path de usuario C:\Javalib\antlr-Versión_mas_reciente-complete.jar y de igual forma el JDK de Java C:\Program Files\Java\jdk-24\bin


Para probar la instalación hacemos lo siguiente
Creamos una carpeta la cual va a contener nuestro archivo .g4 el cual contendrá nuestra gramática
Para verificar que si funciona ejecutamos “java -jar <ubicación_del_jar_de_antlr>” y debe de salirnos algo parecido.
$ C:\Javalib>  java -jar lib\antlr-4.13.2-complete.jar
ANTLR Parser Generator Version 4.6
-o ___ specify output directory where all output is generated
-lib ___ specify location of .tokens files
...

Luego ejecutamos el nuestro archivo .g4 con el comando antlr4 en el CMD
Y luego compilamos con el comando javac
$ cd /Javalib / TuCarpeta
$ javac -cp ".;..\TuRutaRelativa\antlr-4.13.2-complete.jar" Lenguaje.g4 -visitor -listener -o .
$ javac -cp ".;..\TuRutaRelativa\antlr-4.13.2-complete.jar" Lenguaje*.java
para probarlo, creamos un .txt dentro de la misma carpeta el cual contendrá el código a probar
luego corremos el comando para desplegar el Parser Tree que valida tu gramática.
y luego si queremos algo más gráfica de esto podemos usar -gui
java -cp ".;..\TuRutaRelativa\TuCarpeta\antlr-4.13.2-complete.jar" org.antlr.v4.gui.TestRig TuLenguajeg4 program -tree TuCodigoDePrueba.txt

java -cp ".;..\TuRutaRelativa\antlr-4.13.2-complete.jar" org.antlr.v4.gui.TestRig Lenguaje program -gui prueba.txt

Semántica
La clase ‘SemanticVisitor’ extiende ‘LenguajeBaseVisitor’ y se encarga de realizar el análisis semántico del árbol de sintaxis generado por ANTLR4 para el lenguaje. Su principal función es verificar la coherencia y validez semántica del código fuente, como la declaración y uso correcto de variables, tipos, y operaciones.
Campos:
pilaTablasVariables: Stack<Map<String, SemanticData.Variable>>
Una pila que gestiona las tablas de símbolos para los diferentes ámbitos (global y locales de funciones). Cada mapa en la pila representa una tabla de símbolos para un ámbito particular, almacenando el nombre de la variable y su información semántica (SemanticData.Variable).
Estructura: Una pila de mapas.
Por qué: Se utiliza una pila para gestionar los ámbitos de las variables de manera jerárquica. Al entrar en un nuevo ámbito (como una función), se añade un nuevo mapa a la cima de la pila. Al salir del ámbito, se elimina el mapa de la cima. Esto permite implementar el anidamiento de ámbitos y la búsqueda de variables desde el ámbito local hacia el global.
Operaciones
push(new HashMap<>()): Añadir una nueva tabla de símbolos (mapa) al entrar en un nuevo ámbito.
pop(): Eliminar la tabla de símbolos al salir de un ámbito.
peek(): Acceder a la tabla de símbolos del ámbito actual (cima de la pila).
Iteración sobre la pila (de la cima hacia la base) para buscar una variable (buscarVariable).
containsKey(nombreVariable): Verificar si una variable está definida en la tabla de símbolos actual.
put(nombreVariable, variable): Añadir una nueva variable a la tabla de símbolos actual.
get(nombreVariable): Obtener la información de una variable desde la tabla de símbolos.
directorioFunciones: Map<String, SemanticData.Funcion>
Un mapa que almacena información sobre las funciones declaradas en el código fuente. La clave es el nombre de la función y el valor es un objeto SemanticData.Funcion que contiene detalles como el tipo de retorno y los parámetros.
Estructura: Un mapa (diccionario).
Por qué: Se utiliza un mapa para almacenar y acceder eficientemente a la información de las funciones utilizando su nombre como clave. Esto permite verificar la doble declaración de funciones y recuperar información sobre sus parámetros y tipo de retorno al realizar llamadas.
Operaciones
containsKey(nombreFuncion): Verificar si una función ya ha sido declarada.
put(nombreFuncion, funcion): Añadir la información de una nueva función al directorio.
get(nombreFuncion): Obtener la información de una función por su nombre.
keySet(): Obtener el conjunto de nombres de funciones definidas.
cuboSemantico: Map<String, Map<String, Map<String, String>>>
Una estructura de datos tridimensional que define los tipos resultantes de las operaciones entre diferentes tipos. Se utiliza para verificar la validez de las operaciones aritméticas y de comparación.
Estructura: Un mapa anidado (tres niveles de mapas).
Por qué: Esta estructura permite representar de forma organizada las reglas de compatibilidad de tipos para las operaciones. El primer nivel se indexa por el tipo del operando izquierdo, el segundo por el operador, y el tercero por el tipo del operando derecho, devolviendo el tipo resultante de la operación. Esto facilita la verificación de tipos en expresiones aritméticas y de comparación.
Operaciones
containsKey(tipoIzq): Verificar si existe una regla para el tipo izquierdo.
get(tipoIzq): Acceder al mapa de operadores para el tipo izquierdo.
containsKey(operador): Verificar si existe una regla para el operador dado.
get(operador): Acceder al mapa de tipos derechos para el operador.
containsKey(tipoDer): Verificar si existe una regla para el tipo derecho.
get(tipoDer): Obtener el tipo resultante de la operación.
computeIfAbsent(...): Utilizado en la inicialización para asegurar que los mapas internos existan antes de insertar reglas.
tiposValidos: List<String>
Una lista que contiene los nombres de los tipos de datos válidos en tu lenguaje ("int", "float", "str", "bool" - aunque "bool" no aparece explícitamente en el código proporcionado, podría estar implícito en el cubo semántico o en futuras extensiones).
Estructura: Una lista.
Por qué: Se utiliza una lista para mantener un conjunto simple de los nombres de los tipos de datos aceptados por el lenguaje. La operación principal es verificar si un tipo dado pertenece a este conjunto.
Operaciones
contains(tipo): Verificar si un nombre de tipo es válido.
Métodos Privados:
obtenerTipoOperacion(String tipoIzq, String operador, String tipoDer): String
Consulta el cuboSemantico para determinar el tipo resultante de aplicar el operador entre operandos de tipo tipoIzq y tipoDer.
Retorna el tipo resultante si la operación está definida, o null si no lo está.
esTipoCompatible(String tipoEsperado, String tipoRecibido): boolean
Verifica si un tipoRecibido es compatible con un tipoEsperado en un contexto de asignación o paso de parámetros. Actualmente, permite la asignación de int a float.
Retorna true si los tipos son compatibles, false en caso contrario.
buscarVariable(String nombre): SemanticData.Variable
Busca una variable por su nombre en la pila de tablas de símbolos, comenzando desde el ámbito más interno hacia el ámbito global.
Retorna el objeto SemanticData.Variable si la variable se encuentra, o null si no se encuentra en ningún ámbito.
obtenerUltimaFuncionDefinida(): String
Retorna el nombre de la última función que se ha declarado (útil para asociar parámetros a la función actual).
Retorna el nombre de la función o null si no se ha definido ninguna.
obtenerTiposExpresionListOpt(LenguajeParser.Expresion_list_optContext ctx): List<String>
Obtiene una lista de los tipos de las expresiones en una lista de expresiones opcional (expresion_list_opt).
Retorna una lista vacía si la lista de expresiones es nula.
obtenerTiposExpresionList(LenguajeParser.Expresion_listContext ctx): List<String>
Obtiene una lista de los tipos de expresiones en una lista de expresiones (expresion_list).
Retorna una lista de los tipos encontrados.
Métodos Sobrescritos de LenguajeBaseVisitor (para visitar nodos del árbol de sintaxis):
visitProgram(LenguajeParser.ProgramContext ctx): String
Visita el nodo raíz del programa.
Crea el ámbito global, visita las declaraciones globales de variables y funciones, la función main, y luego sale del ámbito global.
visitVars_opt(LenguajeParser.Vars_optContext ctx): String
Visita la sección opcional de declaraciones de variables.
Itera sobre cada declaración de variable (vars) y la visita.
visitFuncs_p(LenguajeParser.Funcs_pContext ctx): String
Visita la secuencia de declaraciones de funciones.
Visita cada declaración de función (funcs) y luego continúa visitando otras funciones.
visitVars(LenguajeParser.VarsContext ctx): String
Visita una declaración de variables.
Obtiene el tipo de las variables y para cada identificador en la lista, verifica si ya ha sido declarado en el ámbito actual y lo agrega a la tabla de símbolos actual.
visitType(LenguajeParser.TypeContext ctx): String
Visita un nodo de tipo.
Retorna el texto del tipo.
visitFuncs(LenguajeParser.FuncsContext ctx): String
Visita una declaración de función.
Obtiene el tipo de retorno y el nombre de la función, verifica si ya existe, la agrega al directorioFunciones, crea un nuevo ámbito para sus variables locales y parámetros, visita los atributos (parámetros), las variables locales (vars_opt), el cuerpo (body), y finalmente sale del ámbito de la función.
visitAtributo(LenguajeParser.AtributoContext ctx): String
Visita la definición de un parámetro en una función.
Obtiene el nombre y tipo del parámetro, verifica su validez y doble declaración, lo agrega a la tabla de símbolos local y a la lista de parámetros de la función en el directorioFunciones.
visitAtr_opt(LenguajeParser.Atr_optContext ctx): String
Visita la parte opcional de más atributos (parámetros) en una función.
Si hay una coma, visita el siguiente atributo.
visitBody(LenguajeParser.BodyContext ctx): String
Visita el cuerpo de un bloque de código (función o main).
Visita la lista de sentencias (statement_list).
visitStatement_list(LenguajeParser.Statement_listContext ctx): String
Visita una lista de sentencias.
Itera sobre cada sentencia (statement) y la visita.
visitStatement(LenguajeParser.StatementContext ctx): String
Visita una sentencia, delegando la visita a la regla específica de la sentencia (asignación, condición, ciclo, llamada a función, impresión).
visitAssing(LenguajeParser.AssingContext ctx): String
Visita una sentencia de asignación.
Obtiene el nombre de la variable, la busca, verifica si está declarada, visita la expresión a la derecha de la asignación y verifica la compatibilidad de tipos.
visitCondition(LenguajeParser.ConditionContext ctx): String
Visita una sentencia condicional (if y if-else).
Visita la expresión de la condición y verifica si su tipo es booleano (aunque "bool" no esté explícito en el código, se espera que la lógica lo maneje). Visita los cuerpos de los bloques if y else.
visitCycle(LenguajeParser.CycleContext ctx): String
Visita una sentencia de ciclo (while).
Visita la expresión de la condición y verifica si su tipo es booleano. Visita el cuerpo del ciclo.
visitF_call(LenguajeParser.F_callContext ctx): String
Visita una llamada a una función.
Obtiene el nombre de la función, verifica si está declarada, obtiene los tipos de los argumentos y verifica si el número y tipo de los argumentos coinciden con los parámetros definidos en el directorioFunciones. Retorna el tipo de retorno de la función.
visitExpresion_list_opt(LenguajeParser.Expresion_list_optContext ctx): String
Visita una lista de expresiones opcional.
Simplemente delega la visita a expresion_list si existe.
visitExpresion_list(LenguajeParser.Expresion_listContext ctx): String
Visita una lista de expresiones.
Itera sobre cada expresión y la visita.
visitPrint(LenguajeParser.PrintContext ctx): String
Visita una sentencia de impresión.
Visita la lista de expresiones que se van a imprimir.
visitExpresion(LenguajeParser.ExpresionContext ctx): String
Visita una expresión, delegando la visita a la regla de comparación.
visitComparacion(LenguajeParser.ComparacionContext ctx): String
Visita una comparación.
Visita las expresiones aritméticas a ambos lados del operador de comparación (si existe) y verifica si la operación de comparación es válida entre sus tipos. Retorna "bool" si hay una comparación, o el tipo de la expresión aritmética si no la hay.
visitAritmetica(LenguajeParser.AritmeticaContext ctx): String
Visita una expresión aritmética (sumas y restas).
Visita los términos y los operadores, obteniendo los tipos y verificando la validez de las operaciones utilizando el cuboSemantico.
visitTermino(LenguajeParser.TerminoContext ctx): String
Visita un término (multiplicaciones y divisiones).
Visita los factores y los operadores, obteniendo los tipos y verificando la validez de las operaciones utilizando el cuboSemantico.
visitFactor(LenguajeParser.FactorContext ctx): String
Visita un factor en una expresión (paréntesis, identificador, constante, operador unario).
Maneja cada caso devolviendo el tipo apropiado o visitando la subexpresión. Busca variables y devuelve sus tipos, y visita las constantes para obtener sus tipos.
visitCte(LenguajeParser.CteContext ctx): String
Visita una constante (entera, flotante o cadena).
Retorna el tipo de la constante ("int", "float", o "str").


Cubo Semántico
Operador
Tipo Izquierdo
Tipo Derecho
Tipo Resultante
+
int
int
int
+
int
float
float
-
int
int
int
-
int
float
float
*
int
int
int
*
int
float
float
/
int
int
float
/
int
float
float
==
int
int
bool
==
int
float
bool
!=
int
int
bool
!=
int
float
bool
>
int
int
bool
>
int
float
bool
<
int
int
bool
<
int
float
bool
=
int
int
int
=
int
float
float
+
float
int
float
+
float
float
float
-
float
int
float
-
float
float
float
*
float
int
float
*
float
float
float
/
float
int
float
/
float
float
float
==
float
int
bool
==
float
float
bool
!=
float
int
bool
!=
float
float
bool
>
float
int
bool
>
float
float
bool
<
float
int
bool
<
float
float
bool
=
float
float
float
+
str
str
str
==
str
str
bool
!=
str
str
bool
=
str
str
str


Código de Expresiones y Estatutos lineales (Cuádruplos)


1.  // Punto 1: Procesamiento de un factor (ID o cte)
    -   Si el factor es un ID:
        -   operandStack.push(id.nombre)
        -   typeStack.push(id.tipo) 
    -   Si el factor es una cte:
        -   operandStack.push(cte.valor)
        -   typeStack.push(cte.tipo)

2.  // Punto 2: Operador Multiplicativo (*, /)
    -   Si el operador actual es '*' o '/':
        -   operatorStack.push(operador)

3.  // Punto 1: Procesamiento de un factor (ID o cte)
    -   (Repetir paso 1)

4.  // Generar cuádruplo para *, /
    -   Si operatorStack.top() es '*' o '/':
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
            -   resultado = generarVariableTemporal()
            -   agregarCuadruplo(operador, operando1, operando2, resultado)
            -   operandStack.push(resultado)
            -   typeStack.push(tipoResultado)
        -   Sino:
            -   ERROR("Error de tipo")

5.  // Punto 3: Operador Aditivo (+, -)
    -   Si el operador actual es '+' o '-':
        -   operatorStack.push(operador)

6.  // Generar cuádruplo para +, -
    -   Si operatorStack.top() es '+' o '-':
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
            -   resultado = generarVariableTemporal()
            -   agregarCuadruplo(operador, operando1, operando2, resultado)
            -   operandStack.push(resultado)
            -   typeStack.push(tipoResultado)
        -   Sino:
            -   ERROR("Error de tipo")

7.  // Punto 4: Operador de Comparación (<, >, ==, !=)
    -    Si el operador actual es de comparación:
         -    operatorStack.push(operador)

8.  // Generar cuádruplo para comparacion
    -   Si operatorStack.top() es un operador de comparación:
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

9.  // Punto 5: Operador AND (&&)
    -   Si el operador actual es '&&':
        -    operatorStack.push("&&")

10.  // Generar cuádruplo para AND
     -  Si operatorStack.top() es "&&":
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

11. // Punto 6: Operador OR (||)
    -   Si el operador actual es '||':
        -   operatorStack.push("||")

12.  // Generar cuádruplo para OR
     -  Si operatorStack.top() es "||":
        -   operando2 = operandStack.pop()
        -   tipoOperando2 = typeStack.pop()
        -   operando1 = operandStack.pop()
        -   tipoOperando1 = typeStack.pop()
        -   operador = operatorStack.pop()
        -   tipoResultado = tablaDeTipos[tipoOperando1, tipoOperando2, operador]
        -   Si tipoResultado != ERROR:
             -  resultado = generarVariableTemporal()
             -  agregarCuadruplo(operador, operando1, operando2, resultado)
             -  operandStack.push(resultado)
             -  typeStack.push(tipoResultado)
        -   Sino:
             -  ERROR ("Error de tipo")

13.  // Punto 7: Fin de la asignación
    -   resultadoExpresion = operandStack.pop()
    -   agregarCuadruplo("=", resultadoExpresion, "", variableAsignacion)

Segmentación de memoria
La memoria virtual se divide en segmentos para almacenar diferentes tipos de datos. A continuación, se muestra una tabla que describe esta segmentación:
Segmento              
Dirección Inicial    
Descripción                                                                    
Variables Globales (int)
1000                
Almacenan variables declaradas fuera de cualquier función.                      
Variables Globales (float)
2000                
Almacenan variables declaradas fuera de cualquier función.                      
Variables Globales (string)
3000                
Almacenan variables declaradas fuera de cualquier función.                      
Variables Locales (int)  
4000                
Almacenan variables declaradas dentro de funciones.                          
Variables Locales (float)  
5000                
Almacenan variables declaradas dentro de funciones.                          
Variables Locales (string)  
6000                
Almacenan variables declaradas dentro de funciones.                          
Variables Temporales (int)
7000                
Almacena los resultados intermedios de cálculos.                              
Variables Temporales (float)
8000                
Almacena los resultados intermedios de cálculos.                              
Variables Temporales (string)
9000                
Almacena los resultados intermedios de cálculos.                              
Variables Temporales (bool)
10000              
Almacena los resultados intermedios de cálculos.                              
Constantes (int)          
11000                
Almacenan valores constantes definidos en el código fuente.                  
Constantes (float)          
12000                
Almacenan valores constantes definidos en el código fuente.                  
Constantes (string)        
13000                
Almacenan valores constantes definidos en el código fuente.                  


Manejo de funciones
Sirve para gestionar el contexto de ejecución de cada función como lo es almacenar variables locales y sus parámetros, guardar la dirección de retorno y tener por separado el ambiente de cada función.

La estructura que usamos para el manejo de las funciones fue un stack llamado de localMemory junto con otras variables para el manejo de lo antes mencionado

// Dirección de retorno (índice del cuádruplo al que regresar)
private int returnAddress;
// Memoria local de la función (parámetros y variables locales)
private Map<Integer, Object> localMemory;
// ID o nombre de la función
private String functionName;
// Dirección base para variables locales
private int baseAddress;



// GeneradorCuadruplosVisitor.java - en visitProgram() 
1. // Generar GOTO inicial a main
- agregarCuadruplo("GOTO", "-1", "-1", "PENDIENTE") // Saltará al main 
- jumpStack.push(quadCounter - 1) // Guardar el índice del GOTO para rellenar después 
- quadCounter++

// GeneradorCuadruplosVisitor.java - en visitFuncs()
2. // Guardar nombre de la función en el directorio y configurar ámbito
    - nombre_funcion = ctx.ID().getText()
    - functionAddresses.put(nombre_funcion, quadCounter) // Mapear nombre a su cuádruplo de inicio
    - info_funcion = SemanticData.directorioFunciones.get(nombre_funcion) // Obtener la información semántica
    - symbolTableStack.push(info_funcion.tablaVariablesLocal) // PUSH la tabla de símbolos local de la función
    - memory.resetLocalAndTemp() // Resetear contadores de memoria local y temporal para este nuevo ámbito


// GeneradorCuadruplosVisitor.java - en visitFuncs() o un método auxiliar llamado desde allí

3. // Contabilizar recursos (ERA) y generar cuádruplos de parámetros
    // Antes de procesar el cuerpo de la función, pero después de asignar direcciones a los parámetros
    // (La asignación de direcciones a parámetros ocurre dentro de visitAtributo)

    // Para cada parámetro (visitAtributo):
    - nombre_param = ctx.ID().getText()
    - tipo_param = SemanticData.directorioFunciones.get(nombre_funcion_actual).parametros.get(idx).tipo
    - variable_param_info = SemanticData.directorioFunciones.get(nombre_funcion_actual).tablaVariablesLocal.get(nombre_param)
    - Si variable_param_info.direccion no ha sido asignada:
        - variable_param_info.direccion = memory.assignLocal(tipo_param) // Asignar dirección local

    // Después de procesar todos los parámetros y variables locales (antes de body):
    // Calcular recursos necesarios para el cuádruplo ERA
    - num_params = info_funcion.parametros.size()
    - num_vars_locales = info_funcion.tablaVariablesLocal.size() - num_params
    - num_temporales_estimado = memory.getCurrentTempCount() // Contabilizar temporales usadas hasta ahora o estimar máximo
    - agregarCuadruplo("ERA", num_params, num_vars_locales, num_temporales_estimado)
    - quadCounter++


// GeneradorCuadruplosVisitor.java - en visitVar_decl() cuando está dentro de una función
4. // Cuádruplos de variables declaradas y contabilización de recursos
    - Al visitar una declaración de variable local (ej. `int a;` dentro de una función):
        - nombre_var = ctx.ID().getText()
        - tipo_var = visit(ctx.type())
        - variable_info = symbolTableStack.peek().get(nombre_var) // Obtener de la tabla local
        - Si variable_info.direccion no ha sido asignada:
            - variable_info.direccion = memory.assignLocal(tipo_var) // Asignar dirección local
        - No se genera cuádruplo explícito para la declaración de variable en sí.
        // La "contabilización de recursos" se actualiza implícitamente por `memory.assignLocal()` y `memory.assignTemp()`.
        // El cuádruplo ERA usa los tamaños finales de los segmentos de memoria para la función.

// GeneradorCuadruplosVisitor.java - en visitBody()
5. // Generación de cuádruplos para el cuerpo de la función
    - Por cada sentencia en el cuerpo (ej. asignaciones, llamadas a funciones, if, while, return):
        - Visitar la sentencia o expresión.
        - Durante la visita, se generarán los cuádruplos apropiados:
            - **Asignaciones:** `("=", dir_origen, -1, dir_destino)`
            - **Operaciones Aritméticas/Lógicas/Relacionales:** `(op, op1_addr, op2_addr, temp_addr)`
            - **Condicionales (`if`):** `("GOTOF", cond_addr, -1, "PENDIENTE")`, `("GOTO", -1, -1, "PENDIENTE")`
            - **Ciclos (`while`):** `("GOTOF", cond_addr, -1, "PENDIENTE")`, `("GOTO", -1, -1, inicio_loop_addr)`
            - **Llamadas a Función:** `("PARAM", arg_addr, -1, -1)` (para cada argumento), `("GOSUB", func_entry_addr, -1, -1)`, `("ASSIGN_RETURN", -1, -1, temp_return_addr)` (si aplica).
            - **Retorno (`return`):** `("RETURN", expr_addr, -1, -1)`.

// GeneradorCuadruplosVisitor.java - en visitReturn()
6. // Generación de cuádruplo RETURN
    - Al visitar una sentencia `return`:
        - `expr_result_addr = "-1"` (por defecto para `void` retorno)
        - Si hay una expresión en el `return`:
            - `expr_result_addr = visit(ctx.expresion())` // Genera cuádruplos para la expresión y obtiene su dirección
            - `typeStack.pop()` // Sacar el tipo de la pila (ya fue validado)
        - `agregarCuadruplo("RETURN", expr_result_addr, "-1", "-1")`
        - `quadCounter++`

// GeneradorCuadruplosVisitor.java - en visitF_call()
7. // Generación de cuádruplos de llamada a función
    - `function_name = ctx.ID().getText()`
    - `func_info = SemanticData.directorioFunciones.get(function_name)`

    // Para cada argumento en `ctx.expresion_list_opt()`:
        - `arg_addr = visit(argumento_expresion)` // Genera cuádruplos para el argumento
        - `typeStack.pop()` // El tipo ya se usó para validación
        - `agregarCuadruplo("PARAM", arg_addr, -1, -1)` // Prepara el argumento para la VM
        - `quadCounter++`

    // Obtener la dirección de inicio de la función
    - `func_start_addr = functionAddresses.get(function_name)`
    - `agregarCuadruplo("GOSUB", func_start_addr, -1, -1)` // Salto a la función
    - `quadCounter++`

    // Si la función retorna un valor:
    - Si `func_info.tipoRetorno` NO es "void":
        - `temp_return_addr = memory.assignTemp(func_info.tipoRetorno)` // Asigna temporal para el resultado
        - `agregarCuadruplo("ASSIGN_RETURN", "-1", "-1", temp_return_addr)` // VM guardará el resultado aquí
        - `quadCounter++`
        - `operandStack.push(temp_return_addr)` // PUSH la dirección de la temporal para usarla en expresiones
        - `typeStack.push(func_info.tipoRetorno)`


// GeneradorCuadruplosVisitor.java - en visitFuncs() (al final)
8. // Finalización de Función (ENDFUNC) y limpieza de ámbito
    - Después de visitar `ctx.body()`:
        - `symbolTableStack.pop()` // POP el ámbito local de la función
        - `agregarCuadruplo("ENDFUNC", "-1", "-1", "-1")` // Marca el final de la función para la VM
        - `quadCounter++`

Máquina Virtual
Sirve para la ejecución de las líneas del código intermedio generado por los  Cuádruplos. Se usaron como estructuras para la máquina virtual Stacks y Maps

Para el funcionamiento de la máquina virtual, consta de 4 archivos
VirtualMachine
ExecutionMemory
VirtualMemory
ActivationRecord

ExecutionMemory
Actúa como el almacenamiento central de la máquina virtual. El cual es responsable de mantener los valores para los diferentes segmentos de memorias, tanto globales, temporales y constantes.
Este se relaciona con VirtualMemory, ya que, ExecutionMemory utiliza las constantes de rango de las direcciones definidas.

Métodos Clave
getValue(int address) : Recupera el valor de la memoria dada su memoria virtual.
setValue(int address, Object value) : Almacena el valor en una dirección virtual específica.


los cuales se llamaran
// Pila de llamadas para funciones
Stack<ActivationRecord> callStack;
// Pila para parámetros de funciones
Stack<Object> parameterStack;
// Mapa de direcciones de funciones
Map<String, Integer> functionAddresses;

VirtualMemory
Esta clase se utiliza principalmente por el generador de cuadruplos para asignar las direcciones virtuales únicas a las variables locales y globales, temporales y constantes.

Métodos clave
assignGlobal(String type) : Asigna una dirección en el segmento global
assignLocal(String type) : Asigna una dirección en el segmento temporal de la función actual
assignTemp(String type) : Asigna una dirección en el segmento temporal de la función
assignConstant(String type) : Asigna una dirección en el segmento de constantes
assignLocalAndTemp(String type) : 
ActivationRecord
Esta estructura se usa para el manejo de funciones en la máquina virtual, ya que, cada vez que se llama a una función se crea una nueva instancia de esta estructura para representar el contexto de ejecución de esa llamada.

Métodos clave
getReturnAddress() : retorna la dirección donde la VM debe continuar después de la función
setParameter(int paramIndex, Object value) : Almacena un valor para un parámetro en la memoria local del registro
getParameter(int paramIndex) : Recupera un valor de parámetro

callStack
Es una pila de objetos ActivationRecord el cual mantiene el orden de las llamadas a funciones anidadas de tipo Stack<ActivationRecord>

Uso:
GOSUB : Push a un nuevo ActivationRecord a la pila.
ENDFUNC : Hace Pop al elemento superior del stack para restaurar el contexto (El superior siempre representa el contexto de la función en ejecución)

parameterStack

Es una pila temporal utilizada para pasar los valores de los argumentos de una función invocadora a la función invocada y es de tipo Stack<Object>

Uso:
PARAM : la función invocadora evalúa un argumento, obtiene su valor y le hace Push al stack parameterStack
GOSUB : La función invocada (dentro de perfomGosub) hace Pop a los valores de parameterStack

functionAddress

Este es un mapa que asocia el nombre de cada función con el índice del cuádruplo donde comienza su implementación.

Uso
execute() : Este se pasa desde el Generador de cuadruplos a la VirtualMachine para que esta sepa donde salta al encontrar un cuádruplo GOSUB
GOSUB: La Maquina virtual consulta functionAddress para obtener el programCounter al que debe salta para iniciar la ejecución de la función.
