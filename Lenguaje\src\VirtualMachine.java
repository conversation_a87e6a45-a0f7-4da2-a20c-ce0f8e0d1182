import java.util.*;

/**
 * Máquina Virtual para ejecutar cuádruplos de BabyDuck.
 * Maneja la ejecución de expresiones, control de flujo y llamadas a funciones.
 */
public class VirtualMachine {

    // Memoria de ejecución
    private ExecutionMemory memory;

    // Pila de llamadas para funciones
    private Stack<ActivationRecord> callStack;

    // Contador de programa (apunta al cuádruplo actual)
    private int programCounter;

    // Lista de cuádruplos a ejecutar
    private List<GeneradorCuadruplosVisitor.Quadruple> quadruples;

    // Pila para parámetros de funciones
    private Stack<Object> parameterStack;

    // Mapa de direcciones de funciones
    private Map<String, Integer> functionAddresses;

    // Flag para controlar la ejecución
    private boolean running;

    // Flag para modo debug
    private boolean debugMode;

    // ActivationRecord temporal para ERA/GOSUB
    private ActivationRecord pendingActivationRecord;

    /**
     * Constructor de la Máquina Virtual.
     */
    public VirtualMachine() {
        this.memory = new ExecutionMemory();
        this.callStack = new Stack<>();
        this.parameterStack = new Stack<>();
        this.functionAddresses = new HashMap<>();
        this.programCounter = 0;
        this.running = false;
        this.debugMode = false;
    }

    /**
     * Ejecuta una lista de cuádruplos.
     * @param quadruples Lista de cuádruplos a ejecutar
     * @param constantValues Mapa de constantes y sus valores
     */
    public void execute(List<GeneradorCuadruplosVisitor.Quadruple> quadruples, Map<Integer, Object> constantValues) {
        this.quadruples = quadruples;
        this.programCounter = 0;
        this.running = true;

        // Inicializar constantes con los valores reales
        initializeConstants(constantValues);

        System.out.println("=== Iniciando ejecución de la Máquina Virtual ===");

        while (running && programCounter < quadruples.size()) {
            GeneradorCuadruplosVisitor.Quadruple currentQuad = quadruples.get(programCounter);

            if (debugMode) {
                System.out.printf("\nExecuting quad %d: %s %d %d %d%n",
                    programCounter,
                    currentQuad.operator,
                    currentQuad.arg1,
                    currentQuad.arg2,
                    currentQuad.result);
            }

            executeQuadruple(currentQuad);

            if (debugMode) {
                printMemoryState();
            }
        }

        System.out.println("=== Ejecución completada ===");
    }

    /**
     * Ejecuta un cuádruplo individual.
     * @param quad Cuádruplo a ejecutar
     */
    private void executeQuadruple(GeneradorCuadruplosVisitor.Quadruple quad) {

        if (debugMode) {
            System.out.println("\nExecuting: " + quad);

            // Añadir debug del registro de activación actual si existe
            if (!callStack.isEmpty()) {
                callStack.peek().printState();
            }
        }

        switch (quad.operator) {
            case "+":
                performArithmetic(quad, (a, b) -> {
                    if (a instanceof Integer && b instanceof Integer) {
                        return (Integer) a + (Integer) b;
                    } else if (a instanceof Float || b instanceof Float) {
                        return ((Number) a).floatValue() + ((Number) b).floatValue();
                    }else if (a instanceof String || b instanceof String) {
                        return ((String) a)+ ((String) b);
                    }
                    throw new RuntimeException("Tipos incompatibles para suma");
                });
                break;

            case "-":
                performArithmetic(quad, (a, b) -> {
                    if (a instanceof Integer && b instanceof Integer) {
                        return (Integer) a - (Integer) b;
                    } else if (a instanceof Float || b instanceof Float) {
                        return ((Number) a).floatValue() - ((Number) b).floatValue();
                    }
                    throw new RuntimeException("Tipos incompatibles para resta");
                });
                break;

            case "*":
                performArithmetic(quad, (a, b) -> {
                    if (a instanceof Integer && b instanceof Integer) {
                        return (Integer) a * (Integer) b;
                    } else if (a instanceof Float || b instanceof Float) {
                        return ((Number) a).floatValue() * ((Number) b).floatValue();
                    }
                    throw new RuntimeException("Tipos incompatibles para multiplicación");
                });
                break;

            case "/":
                performArithmetic(quad, (a, b) -> {
                    if (((Number) b).doubleValue() == 0) {
                        throw new RuntimeException("División por cero");
                    }
                    if (a instanceof Integer && b instanceof Integer) {
                        return (Integer) a / (Integer) b;
                    } else {
                        return ((Number) a).floatValue() / ((Number) b).floatValue();
                    }
                });
                break;

            case "=":
                performAssignment(quad);
                break;

            case ">":
                performComparison(quad, (a, b) -> ((Number) a).doubleValue() > ((Number) b).doubleValue());
                break;

            case "<":
                performComparison(quad, (a, b) -> ((Number) a).doubleValue() < ((Number) b).doubleValue());
                break;

            case "==":
                performComparison(quad, (a, b) -> Objects.equals(a, b));
                break;

            case "!=":
                performComparison(quad, (a, b) -> !Objects.equals(a, b));
                break;

            case ">=":
                performComparison(quad, (a, b) -> ((Number) a).doubleValue() >= ((Number) b).doubleValue());
                break;

            case "<=":
                performComparison(quad, (a, b) -> ((Number) a).doubleValue() <= ((Number) b).doubleValue());
                break;

            case "&&":
                performLogical(quad, (a, b) -> (Boolean) a && (Boolean) b);
                break;

            case "||":
                performLogical(quad, (a, b) -> (Boolean) a || (Boolean) b);
                break;

            case "GOTO":
                programCounter = quad.result;
                return; // No incrementar PC

            case "GOTOF":
                performGotoF(quad);
                return;

            case "GOTOT":
                performGotoT(quad);
                break;

            case "PRINT":
                performPrint(quad);
                break;

            case "ERA":
                performERA(quad);
                break;

            case "PARAM":
                performParam(quad);
                break;

            case "GOSUB":
                performGosub(quad);
                return; // No incrementar PC automáticamente

            case "ENDFUNC":
                performEndFunc();
                return; // No incrementar PC automáticamente

            default:
                throw new RuntimeException("Operador no soportado: " + quad.operator);
        }

        // Incrementar contador de programa (excepto para saltos)
        programCounter++;
    }

    /**
     * Inicializa las constantes en memoria usando el mapa de constantes del generador.
     */
    private void initializeConstants(Map<Integer, Object> constantValues) {
        // Inicializar todas las constantes del mapa
        for (Map.Entry<Integer, Object> entry : constantValues.entrySet()) {
            memory.initializeConstant(entry.getKey(), entry.getValue());
        }
    }

    /**
     * Obtiene un valor de memoria, considerando el contexto actual.
     * @param address Dirección virtual
     * @return Valor almacenado
     */
    private Object getMemoryValue(int address) {
        // Si es una dirección local y hay un contexto de función activo
        if (address >= VirtualMemory.LOCAL_INT_START && address < VirtualMemory.TEMP_INT_START && !callStack.isEmpty()) {
            ActivationRecord currentRecord = callStack.peek();
            // Convertir dirección relativa a dirección absoluta basada en la base address
            int relativeAddress = address - VirtualMemory.LOCAL_INT_START;
            int absoluteAddress = currentRecord.getBaseAddress() + relativeAddress;
            return currentRecord.getLocalValue(absoluteAddress);
        } else {
            return memory.getValue(address);
        }
    }

    /**
     * Establece un valor en memoria, considerando el contexto actual.
     * @param address Dirección virtual
     * @param value Valor a almacenar
     */
    private void setMemoryValue(int address, Object value) {
        if (debugMode) {
            System.out.println("Setting memory - Address: " + address + ", Value: " + value);
        }

        // Si es una dirección local y hay un contexto de función activo
        if (address >= VirtualMemory.LOCAL_INT_START && address < VirtualMemory.TEMP_INT_START && !callStack.isEmpty()) {
            ActivationRecord currentRecord = callStack.peek();
            // Convertir dirección relativa a dirección absoluta basada en la base address
            int relativeAddress = address - VirtualMemory.LOCAL_INT_START;
            int absoluteAddress = currentRecord.getBaseAddress() + relativeAddress;
            currentRecord.setLocalValue(absoluteAddress, value);
        } else {
            memory.setValue(address, value);
        }
    }

    /**
     * Habilita o deshabilita el modo debug.
     * @param debug true para habilitar debug, false para deshabilitar
     */
    public void setDebugMode(boolean debug) {
        this.debugMode = debug;
    }

    /**
     * Detiene la ejecución de la máquina virtual.
     */
    public void stop() {
        this.running = false;
    }

    /**
     * Imprime el estado de la memoria (variables globales y temporales). <---------------------- METODO PARA DEBUGUEAR *************
     */
    public void printMemoryState() {
        System.out.println("Global Variables:");
        // Print variables from 1000-1999
        for (int i = 1000; i < 2000; i++) {
            Object value = memory.getValue(i);
            if (value != null) {
                System.out.printf("Address %d: %s%n", i, value);
            }
        }

        System.out.println("\nTemporary Variables:");
        // Print temporals from 7000-7999
        for (int i = 7000; i < 8000; i++) {
            Object value = memory.getValue(i);
            if (value != null) {
                System.out.printf("Address %d: %s%n", i, value);
            }
        }
    }

    // ===== MÉTODOS AUXILIARES PARA OPERACIONES =====

    /**
     * Interfaz funcional para operaciones aritméticas.
     */
    @FunctionalInterface
    private interface ArithmeticOperation {
        Object apply(Object a, Object b);
    }

    /**
     * Interfaz funcional para operaciones de comparación.
     */
    @FunctionalInterface
    private interface ComparisonOperation {
        Boolean apply(Object a, Object b);
    }

    /**
     * Interfaz funcional para operaciones lógicas.
     */
    @FunctionalInterface
    private interface LogicalOperation {
        Boolean apply(Object a, Object b);
    }

    /**
     * Realiza una operación aritmética.
     */
    private void performArithmetic(GeneradorCuadruplosVisitor.Quadruple quad, ArithmeticOperation operation) {
        Object val1 = getMemoryValue(quad.arg1);
        Object val2 = getMemoryValue(quad.arg2);

        if (val1 == null || val2 == null) {
            throw new RuntimeException(String.format(
                "Null value in arithmetic operation at quad %d: %s %s %s -> %s (values: %s, %s)",
                programCounter, quad.operator, quad.arg1, quad.arg2, quad.result, val1, val2
            ));
        }

        Object result = operation.apply(val1, val2);

        if (result == null) {
            throw new RuntimeException("Arithmetic operation returned null");
        }

        setMemoryValue(quad.result, result);

        // Debug para verificar valores
        if (debugMode) {
            System.out.printf("Arithmetic operation: %s %s %s = %s (stored at %d)%n",
                val1, quad.operator, val2, result, quad.result);
            memory.printMemoryState();
        }
    }

    /**
     * Realiza una asignación.
     */
    private void performAssignment(GeneradorCuadruplosVisitor.Quadruple quad) {
        Object value = getMemoryValue(quad.arg1);
        setMemoryValue(quad.result, value);

        // Debug para verificar asignación
        if (debugMode) {
            System.out.println("Assignment: address " + quad.result + " = " + value);
        }
    }

    /**
     * Realiza una operación de comparación.
     */
    private void performComparison(GeneradorCuadruplosVisitor.Quadruple quad, ComparisonOperation operation) {
        Object val1 = getMemoryValue(quad.arg1);
        Object val2 = getMemoryValue(quad.arg2);

        // Validación de nulos
        if (val1 == null || val2 == null) {
            throw new RuntimeException(String.format(
                "Null value in comparison at quad %d: %s %s %s -> %s (values: %s, %s)",
                programCounter, quad.operator, quad.arg1, quad.arg2, quad.result, val1, val2
            ));
        }

        // Verificar que sean números
        if (!(val1 instanceof Number) || !(val2 instanceof Number)) {
            throw new RuntimeException(String.format(
                "Non-numeric comparison at quad %d: %s %s %s -> %s (types: %s, %s)",
                programCounter, quad.operator, quad.arg1, quad.arg2, quad.result,
                val1.getClass().getSimpleName(), val2.getClass().getSimpleName()
            ));
        }

        Boolean result = operation.apply(val1, val2);
        setMemoryValue(quad.result, result);

        if (debugMode) {
            System.out.printf("Comparison: %s %s %s = %s%n", val1, quad.operator, val2, result);
        }
    }

    /**
     * Realiza una operación lógica.
     */
    private void performLogical(GeneradorCuadruplosVisitor.Quadruple quad, LogicalOperation operation) {
        Object val1 = getMemoryValue(quad.arg1);
        Object val2 = getMemoryValue(quad.arg2);
        Boolean result = operation.apply(val1, val2);
        setMemoryValue(quad.result, result);
    }

    /**
     * Realiza un salto condicional si falso.
     */
    private void performGotoF(GeneradorCuadruplosVisitor.Quadruple quad) {
        Object condition = getMemoryValue(quad.arg1);
        if (condition instanceof Boolean && !(Boolean) condition) {
            programCounter = quad.result;
            return; // No incrementar PC
        } else {
            programCounter++;
        }
        // Si la condición es verdadera, continuar con la siguiente instrucción
        // El incremento del PC se hace automáticamente en executeQuadruple()
    }

    /**
     * Realiza un salto condicional si verdadero.
     */
    private void performGotoT(GeneradorCuadruplosVisitor.Quadruple quad) {
        Object condition = getMemoryValue(quad.arg1);
        if (condition instanceof Boolean && (Boolean) condition) {
            programCounter = quad.result;
            return; // No incrementar PC
        }
        // Si la condición es falsa, continuar con la siguiente instrucción
        // El incremento del PC se hace automáticamente en executeQuadruple()
    }

    /**
     * Realiza una operación de impresión.
     */
    private void performPrint(GeneradorCuadruplosVisitor.Quadruple quad) {
        Object value = getMemoryValue(quad.arg1);
        System.out.println("OUTPUT: " + value);
    }

    /**
     * Crea un Espacio de Registro de Activación (ERA).
     */
    private void performERA(GeneradorCuadruplosVisitor.Quadruple quad) {
        // El arg1 contiene el hash del nombre de la función
        String functionName = "func_" + quad.arg1;
        int baseAddress = VirtualMemory.LOCAL_INT_START + (callStack.size() * 1000);

        if (debugMode) {
            System.out.println("ERA: reservando recursos para: " + functionName);
        }

        // Crear el ActivationRecord y guardarlo para GOSUB
        // La dirección de retorno se establecerá en GOSUB
        pendingActivationRecord = new ActivationRecord(functionName, -1, baseAddress);
    }

    /**
     * Maneja el paso de parámetros.
     */
    private void performParam(GeneradorCuadruplosVisitor.Quadruple quad) {
        Object paramValue = getMemoryValue(quad.arg1);
        if (debugMode) {
            System.out.println("PARAM: Agregando parámetro " + paramValue + " desde dirección " + quad.arg1);
        }
        parameterStack.push(paramValue);
    }

    /**
     * Realiza una llamada a subrutina (función).
     */
    private void performGosub(GeneradorCuadruplosVisitor.Quadruple quad) {
        // Usar el ActivationRecord creado en ERA
        if (pendingActivationRecord == null) {
            throw new RuntimeException("GOSUB sin ERA previo");
        }

        if (debugMode) {
            System.out.println("GOSUB: Llamando a función en dirección " + quad.arg1);
            System.out.println("GOSUB: Parámetros en pila: " + parameterStack.size());
        }

        // Asignar parámetros desde la pila (en orden correcto)
        // Los parámetros están en la pila en orden inverso, así que los sacamos y los asignamos
        List<Object> params = new ArrayList<>();
        while (!parameterStack.isEmpty()) {
            params.add(parameterStack.pop());
        }

        // Asignar parámetros (invertir la lista)
        for (int i = params.size() - 1; i >= 0; i--) {
            Object paramValue = params.get(i);
            int paramIndex = params.size() - i - 1; // Índice correcto
            pendingActivationRecord.setParameter(paramIndex, paramValue);
            if (debugMode) {
                System.out.println("GOSUB: Seteando parámetro " + paramIndex + " = " + paramValue);
            }
        }

        // Establecer la dirección de retorno correcta (después del GOSUB)
        pendingActivationRecord.setReturnAddress(programCounter + 1);

        // Hacer push del registro de activación
        callStack.push(pendingActivationRecord);
        pendingActivationRecord = null; // Limpiar

        // Saltar a la función
        programCounter = quad.arg1;
        return; // No incrementar PC automáticamente

    }

    /**
     * Maneja el final de una función.
     */
    private void performEndFunc() {
        if (!callStack.isEmpty()) {
            ActivationRecord record = callStack.pop();
            programCounter = record.getReturnAddress();
            return; // No incrementar PC automáticamente
        } else {
            // Si no hay más funciones, terminar ejecución
            running = false;
        }
    }
}
