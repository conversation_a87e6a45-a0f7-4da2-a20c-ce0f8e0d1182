# Manual de ExecutionMemory.java

## Propósito General
La clase `ExecutionMemory` es el componente central de gestión de memoria de la Máquina Virtual. Su función principal es administrar los diferentes segmentos de memoria durante la ejecución del programa compilado, proporcionando una interfaz unificada para acceder y modificar valores en memoria virtual.

## Arquitectura de Memoria

### Segmentos de Memoria
La clase maneja cuatro segmentos principales de memoria:

1. **Memoria Global** (`globalMemory`)
   - Almacena variables globales del programa
   - Rango de direcciones: 1000-3999 (definido en VirtualMemory)
   - Persiste durante toda la ejecución del programa

2. **Memoria de Constantes** (`constantMemory`)
   - Almacena valores constantes (literales)
   - Rango de direcciones: 11000+ (definido en VirtualMemory)
   - Solo lectura durante la ejecución

3. **Memoria Temporal** (`tempMemory`)
   - Almacena resultados intermedios de operaciones
   - Rango de direcciones: 7000-10999 (definido en VirtualMemory)
   - Se utiliza para cálculos y expresiones complejas

4. **Memoria Local** (NO manejada directamente)
   - Las direcciones locales (4000-6999) son redirigidas a `ActivationRecord`
   - Esto permite el manejo correcto de ámbitos de funciones

## Estructura de Datos

### Variables de Instancia
```java
private Map<Integer, Object> globalMemory;    // Variables globales
private Map<Integer, Object> constantMemory;  // Constantes
private Map<Integer, Object> tempMemory;      // Variables temporales
```

Todas utilizan `HashMap<Integer, Object>` donde:
- **Clave (Integer)**: Dirección virtual de memoria
- **Valor (Object)**: Valor almacenado (puede ser Integer, Float, String, Boolean)

## Métodos Principales

### Constructor
```java
public ExecutionMemory()
```
- Inicializa los tres mapas de memoria
- No requiere parámetros
- Crea estructuras vacías listas para uso

### Método getValue(int address)
**Propósito**: Recuperar un valor de memoria basado en dirección virtual

**Flujo de Ejecución**:
1. Determina el segmento de memoria según la dirección
2. Si es memoria global (1000-3999): consulta `globalMemory`
3. Si es memoria local (4000-6999): retorna `null` (debe usar ActivationRecord)
4. Si es memoria temporal (7000-10999): consulta `tempMemory`
5. Si es memoria de constantes (11000+): consulta `constantMemory`
6. Si la dirección es inválida: lanza excepción

**Casos Especiales**:
- Direcciones locales retornan `null` intencionalmente
- La VirtualMachine debe manejar estas direcciones con ActivationRecord

### Método setValue(int address, Object value)
**Propósito**: Almacenar un valor en memoria basado en dirección virtual

**Flujo de Ejecución**:
1. Determina el segmento de memoria según la dirección
2. Si es memoria global: almacena en `globalMemory`
3. Si es memoria local: lanza excepción (debe usar ActivationRecord)
4. Si es memoria temporal: almacena en `tempMemory`
5. Si es memoria de constantes: almacena en `constantMemory`
6. Si la dirección es inválida: lanza excepción

**Validaciones**:
- No permite escritura directa en direcciones locales
- Valida que la dirección esté en un rango válido

### Método initializeConstant(int address, Object value)
**Propósito**: Inicializar constantes durante la fase de setup

**Características**:
- Método especializado para constantes
- Se usa durante la inicialización de la VM
- Almacena directamente en `constantMemory`

### Métodos de Utilidad

#### getGlobalMemory()
- Retorna una copia de la memoria global
- Útil para debugging y análisis
- Previene modificaciones accidentales

#### getTempMemory()
- Retorna una copia de la memoria temporal
- Útil para debugging
- Permite inspección sin modificación

#### getConstantMemory()
- Retorna una copia de la memoria de constantes
- Útil para verificar valores inicializados

#### printMemoryState()
- Método de debugging
- Imprime el contenido de todos los segmentos
- Muestra direcciones y valores almacenados

## Interacción con Otros Componentes

### Con VirtualMachine
- VirtualMachine usa ExecutionMemory como almacén principal
- VirtualMachine maneja las direcciones locales con ActivationRecord
- VirtualMachine inicializa constantes a través de `initializeConstant()`

### Con ActivationRecord
- ExecutionMemory NO maneja memoria local directamente
- ActivationRecord gestiona variables locales y parámetros
- Separación clara de responsabilidades

### Con VirtualMemory
- VirtualMemory define los rangos de direcciones
- ExecutionMemory usa estos rangos para determinar segmentos
- Coordinación en la asignación de direcciones virtuales

## Flujo de Ejecución Típico

### Durante Inicialización
1. Se crea una instancia de ExecutionMemory
2. Se inicializan las constantes con `initializeConstant()`
3. Se preparan los segmentos de memoria

### Durante Ejecución
1. VirtualMachine solicita valores con `getValue()`
2. ExecutionMemory determina el segmento apropiado
3. Retorna el valor o `null` si es dirección local
4. VirtualMachine almacena resultados con `setValue()`
5. ExecutionMemory valida y almacena en el segmento correcto

### Durante Debugging
1. Se puede llamar `printMemoryState()` para inspección
2. Los métodos `get*Memory()` permiten análisis detallado
3. Útil para verificar el estado de variables y temporales

## Consideraciones de Diseño

### Separación de Responsabilidades
- ExecutionMemory: memoria global, temporal y constantes
- ActivationRecord: memoria local y parámetros
- VirtualMemory: asignación de direcciones virtuales

### Manejo de Errores
- Excepciones claras para direcciones inválidas
- Validación de rangos de memoria
- Mensajes descriptivos para debugging

### Eficiencia
- Uso de HashMap para acceso O(1)
- Separación de segmentos para mejor organización
- Métodos de copia para prevenir modificaciones accidentales

## Casos de Uso Comunes

### Almacenar Variable Global
```java
memory.setValue(1000, 42);  // Variable global en dirección 1000
```

### Recuperar Resultado Temporal
```java
Object result = memory.getValue(7000);  // Temporal en dirección 7000
```

### Inicializar Constante
```java
memory.initializeConstant(11000, "Hello World");  // Constante string
```

### Debugging
```java
memory.printMemoryState();  // Ver estado completo de memoria
```
