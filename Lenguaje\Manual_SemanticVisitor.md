# Manual de SemanticVisitor.java

## Propósito General
La clase `SemanticVisitor` es responsable del análisis semántico del programa. Extiende `LenguajeBaseVisitor<String>` y recorre el Árbol de Sintaxis Abstracta (AST) para verificar la correctitud semántica del código fuente, incluyendo declaración de variables, compatibilidad de tipos, existencia de funciones y validación de operaciones.

## Concepto de Análisis Semántico
El análisis semántico verifica que el programa sea semánticamente correcto:
- **Declaración de Variables**: Todas las variables deben estar declaradas antes de usarse
- **Compatibilidad de Tipos**: Las operaciones deben ser válidas entre los tipos involucrados
- **<PERSON><PERSON><PERSON><PERSON> (Scopes)**: Variables locales vs globales, visibilidad correcta
- **Funciones**: Declaración, llamadas con parámetros correctos
- **Operaciones**: Validación usando cubo semántico

## Estructura de Datos

### Clase de Excepción
```java
public static class SemanticError extends RuntimeException {
    public SemanticError(String message) {
        super(message);
    }
}
```

### Variables de Instancia
```java
private Stack<Map<String, SemanticData.Variable>> pilaTablasVariables = new Stack<>();
private Map<String, SemanticData.Funcion> directorioFunciones = SemanticData.directorioFunciones;
private Map<String, Map<String, Map<String, String>>> cuboSemantico = SemanticData.cuboSemantico;
private List<String> tiposValidos = SemanticData.tiposValidos;
```

### Detalles de Componentes

#### pilaTablasVariables
- **Propósito**: Maneja ámbitos de variables (scoping)
- **Estructura**: Pila de mapas, cada mapa representa un ámbito
- **Funcionamiento**: 
  - Base: tabla global
  - Push: al entrar a función
  - Pop: al salir de función
  - Búsqueda: desde el tope hacia la base

#### directorioFunciones
- **Propósito**: Almacena información de todas las funciones declaradas
- **Contenido**: Nombre, tipo de retorno, parámetros
- **Uso**: Validación de llamadas a función

#### cuboSemantico
- **Propósito**: Define operaciones válidas entre tipos
- **Estructura**: `Map<TipoIzq, Map<Operador, Map<TipoDer, TipoResultado>>>`
- **Uso**: Validar compatibilidad de operaciones

## Métodos de Validación Semántica

### obtenerTipoOperacion()
```java
private String obtenerTipoOperacion(String tipoIzq, String operador, String tipoDer)
```

**Propósito**: Consulta el cubo semántico para validar operaciones

**Flujo**:
1. Busca en cuboSemantico[tipoIzq][operador][tipoDer]
2. Retorna tipo resultante si la operación es válida
3. Retorna null si la operación no está definida

**Ejemplo**:
- `obtenerTipoOperacion("int", "+", "int")` → "int"
- `obtenerTipoOperacion("int", "+", "float")` → "float"
- `obtenerTipoOperacion("int", "+", "str")` → null (inválido)

### esTipoCompatible()
```java
private boolean esTipoCompatible(String tipoEsperado, String tipoRecibido)
```

**Reglas de Compatibilidad**:
- Tipos idénticos son compatibles
- `int` es compatible con `float` (promoción automática)
- Otras conversiones no son automáticas

### buscarVariable()
```java
private SemanticData.Variable buscarVariable(String nombre)
```

**Algoritmo de Búsqueda**:
1. Recorre la pila desde el tope (ámbito más local)
2. Busca en cada tabla de símbolos
3. Retorna la primera ocurrencia encontrada
4. Retorna null si no se encuentra

**Implementación del Scoping**:
- Variables locales ocultan variables globales del mismo nombre
- Búsqueda respeta jerarquía de ámbitos

## Métodos Visitor Principales

### visitProgram()
**Flujo de Análisis**:
1. Asegura que la tabla global esté en la pila
2. Analiza declaraciones de variables globales
3. Analiza declaraciones de funciones
4. Analiza el cuerpo principal (main)
5. Mantiene tabla global para GeneradorCuadruplos

### visitVars()
**Validación de Declaraciones**:
```java
public String visitVars(LenguajeParser.VarsContext ctx) {
    String tipo = ctx.type().getText();
    if (!tiposValidos.contains(tipo)) {
        throw new SemanticError("Tipo de variable inválido: " + tipo);
    }
    
    for (TerminalNode idNode : ctx.id_list().ID()) {
        String nombreVariable = idNode.getText();
        Map<String, SemanticData.Variable> tablaActual = pilaTablasVariables.peek();
        if (tablaActual.containsKey(nombreVariable)) {
            throw new SemanticError("Variable doblemente declarada: " + nombreVariable);
        }
        tablaActual.put(nombreVariable, new SemanticData.Variable(nombreVariable, tipo));
    }
    return null;
}
```

**Validaciones**:
- Tipo debe estar en lista de tipos válidos
- Variable no debe estar ya declarada en el ámbito actual
- Agrega variable a la tabla de símbolos actual

### visitFuncs()
**Gestión de Funciones**:
```java
public String visitFuncs(LenguajeParser.FuncsContext ctx) {
    String tipoRetorno = ctx.VOID() != null ? ctx.VOID().getText() : null;
    String nombreFuncion = ctx.ID().getText();
    
    if (directorioFunciones.containsKey(nombreFuncion)) {
        throw new SemanticError("Función doblemente declarada: " + nombreFuncion);
    }
    
    SemanticData.Funcion funcion = new SemanticData.Funcion(nombreFuncion, tipoRetorno);
    directorioFunciones.put(nombreFuncion, funcion);
    pilaTablasVariables.push(new HashMap<>()); // Nuevo ámbito
    
    visit(ctx.atributo());     // Procesar parámetros
    visit(ctx.vars_opt());     // Variables locales
    visit(ctx.body());         // Cuerpo de función
    
    pilaTablasVariables.pop(); // Salir del ámbito
    return null;
}
```

**Gestión de Ámbitos**:
- Crea nuevo ámbito local para la función
- Procesa parámetros y variables locales
- Restaura ámbito anterior al terminar

### visitAtributo()
**Procesamiento de Parámetros**:
```java
public String visitAtributo(LenguajeParser.AtributoContext ctx) {
    if (ctx.ID() != null) {
        String nombreParametro = ctx.ID().getText();
        String tipoParametro = ctx.type().getText();
        
        // Validar tipo
        if (!tiposValidos.contains(tipoParametro)) {
            throw new SemanticError("Tipo de parámetro inválido: " + tipoParametro);
        }
        
        // Verificar no duplicación en ámbito local
        Map<String, SemanticData.Variable> tablaLocal = pilaTablasVariables.peek();
        if (tablaLocal.containsKey(nombreParametro)) {
            throw new SemanticError("Parámetro doblemente declarado: " + nombreParametro);
        }
        
        // Agregar a tabla local y a función
        tablaLocal.put(nombreParametro, new SemanticData.Variable(nombreParametro, tipoParametro));
        SemanticData.Funcion funcionActual = directorioFunciones.get(obtenerUltimaFuncionDefinida());
        funcionActual.parametros.add(new SemanticData.Variable(nombreParametro, tipoParametro));
        
        visit(ctx.atr_opt()); // Procesar más parámetros
    }
    return null;
}
```

### visitAssing()
**Validación de Asignaciones**:
```java
public String visitAssing(LenguajeParser.AssingContext ctx) {
    String nombreVariable = ctx.ID().getText();
    SemanticData.Variable variable = buscarVariable(nombreVariable);
    
    if (variable == null) {
        throw new SemanticError("Variable no declarada: " + nombreVariable);
    }
    
    String tipoExpr = visit(ctx.expresion());
    if (tipoExpr != null && !esTipoCompatible(variable.tipo, tipoExpr)) {
        throw new SemanticError("Tipos incompatibles en la asignación: " + 
                               variable.tipo + " y " + tipoExpr);
    }
    return null;
}
```

**Validaciones**:
- Variable debe estar declarada (búsqueda en ámbitos)
- Tipo de expresión debe ser compatible con tipo de variable

### visitF_call()
**Validación de Llamadas a Función**:
```java
public String visitF_call(LenguajeParser.F_callContext ctx) {
    String nombreFuncion = ctx.ID().getText();
    SemanticData.Funcion funcion = directorioFunciones.get(nombreFuncion);
    
    if (funcion == null) {
        throw new SemanticError("Función no declarada: " + nombreFuncion);
    }
    
    List<String> tiposArgumentos = obtenerTiposExpresionListOpt(ctx.expresion_list_opt());
    
    // Validar número de argumentos
    if (funcion.parametros.size() != tiposArgumentos.size()) {
        throw new SemanticError("Número incorrecto de argumentos en la llamada a " + nombreFuncion);
    }
    
    // Validar tipos de argumentos
    for (int i = 0; i < funcion.parametros.size(); i++) {
        String tipoArg = tiposArgumentos.get(i);
        if (tipoArg != null && !esTipoCompatible(funcion.parametros.get(i).tipo, tipoArg)) {
            throw new SemanticError("Tipo de argumento incorrecto en la llamada a " + nombreFuncion);
        }
    }
    
    return funcion.tipoRetorno;
}
```

**Validaciones**:
- Función debe estar declarada
- Número de argumentos debe coincidir
- Tipos de argumentos deben ser compatibles

## Validación de Expresiones

### visitExpresion() y Jerarquía
- `visitExpresion()`: Maneja OR lógico (||)
- `visitAndExpr()`: Maneja AND lógico (&&)
- `visitComparacion()`: Maneja operadores relacionales
- `visitAritmetica()`: Maneja + y -
- `visitTermino()`: Maneja * y /
- `visitFactor()`: Maneja factores básicos

### Validación de Operaciones
```java
public String visitComparacion(LenguajeParser.ComparacionContext ctx) {
    String tipoIzq = visit(ctx.aritmetica(0));
    if (ctx.getChildCount() > 1) {
        String operador = ctx.getChild(1).getText();
        String tipoDer = visit(ctx.aritmetica(1));
        String tipoResultado = obtenerTipoOperacion(tipoIzq, operador, tipoDer);
        
        if (tipoResultado == null || !tipoResultado.equals("bool")) {
            throw new SemanticError("Operación de comparación inválida entre tipos: " + 
                                   tipoIzq + " " + operador + " " + tipoDer);
        }
        return "bool";
    }
    return tipoIzq;
}
```

### visitCondition() y visitCycle()
**Validación de Condiciones**:
```java
public String visitCondition(LenguajeParser.ConditionContext ctx) {
    String tipoCondicion = visit(ctx.expresion());
    if (tipoCondicion != null && !tipoCondicion.equals("bool")) {
        throw new SemanticError("La condición debe ser de tipo booleano, pero se encontró: " + tipoCondicion);
    }
    visit(ctx.body(0));
    if (ctx.ELSE() != null) {
        visit(ctx.body(1));
    }
    return null;
}
```

## Métodos de Utilidad

### obtenerUltimaFuncionDefinida()
- Retorna el nombre de la última función agregada al directorio
- Usado para asociar parámetros con la función correcta

### obtenerTiposExpresionListOpt()
- Extrae tipos de una lista de expresiones
- Usado para validar argumentos en llamadas a función

## Flujo de Análisis Típico

### Programa Simple
1. **Variables Globales**: Validar declaraciones y tipos
2. **Cuerpo Principal**: Validar statements y expresiones
3. **Verificación**: Todas las variables usadas están declaradas

### Programa con Funciones
1. **Variables Globales**: Procesar declaraciones globales
2. **Declaraciones de Función**: 
   - Validar no duplicación
   - Crear ámbito local
   - Procesar parámetros y variables locales
   - Validar cuerpo de función
3. **Cuerpo Principal**: Validar con acceso a funciones declaradas

### Validación de Expresiones
1. **Factores**: Validar variables declaradas, tipos de constantes
2. **Operaciones**: Consultar cubo semántico para validez
3. **Propagación**: Propagar tipos hacia arriba en la jerarquía
4. **Contexto**: Validar tipos en asignaciones y condiciones

## Manejo de Errores

### Tipos de Errores Semánticos
- **Variables no declaradas**: Uso antes de declaración
- **Doble declaración**: Variable/función declarada múltiples veces
- **Tipos incompatibles**: Operaciones entre tipos no válidos
- **Argumentos incorrectos**: Número o tipos de parámetros incorrectos
- **Condiciones no booleanas**: Condiciones que no evalúan a bool

### Estrategia de Manejo
- Lanzar `SemanticError` con mensaje descriptivo
- Incluir número de línea cuando está disponible
- Terminar análisis en primer error encontrado
- Mensajes claros para facilitar debugging

## Consideraciones de Diseño

### Eficiencia
- Búsqueda en ámbitos desde local hacia global
- Uso de HashMap para acceso O(1) a símbolos
- Pila para gestión eficiente de ámbitos

### Flexibilidad
- Cubo semántico configurable externamente
- Tipos válidos definidos en SemanticData
- Extensible para nuevos tipos y operaciones

### Robustez
- Validación exhaustiva de todas las construcciones
- Manejo correcto de ámbitos anidados
- Propagación correcta de tipos en expresiones
