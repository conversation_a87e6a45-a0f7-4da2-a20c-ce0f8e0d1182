# Manual de GeneradorCuadruplosVisitor.java

## Propósito General
La clase `GeneradorCuadruplosVisitor` es responsable de la generación de código intermedio en forma de cuádruplos. Extiende `LenguajeBaseVisitor<String>` y recorre el Árbol de Sintaxis Abstracta (AST) para traducir las construcciones del lenguaje fuente a una representación intermedia ejecutable por la Máquina Virtual.

## Concepto de Cuádruplos
Un cuádruplo es una representación de código intermedio que contiene:
- **Operador**: La operación a realizar (+, -, *, /, =, GOTO, etc.)
- **Arg1**: Primer operando (dirección virtual)
- **Arg2**: Segundo operando (dirección virtual)
- **Result**: Dirección donde almacenar el resultado

## Estructuras de Datos Principales

### Pilas de Operación
```java
private Stack<String> operatorStack = new Stack<>();    // Operadores pendientes
private Stack<String> operandStack = new Stack<>();     // Operandos pendientes
private Stack<String> typeStack = new Stack<>();        // Tipos de operandos
```

### Pilas de Control de Flujo
```java
private Stack<Integer> jumpStack = new Stack<>();       // Saltos para if/while
private Stack<Integer> functionStack = new Stack<>();   // Direcciones de funciones
```

### Mapas y Listas de Gestión
```java
private Map<String, Integer> functionAddresses = new HashMap<>();     // Direcciones de funciones
private List<Quadruple> quadruples = new ArrayList<>();               // Lista de cuádruplos
private Map<String, Integer> operandToAddress = new HashMap<>();      // Operandos a direcciones
private Map<Integer, Object> constantValues = new HashMap<>();        // Valores de constantes
```

### Contadores y Estado
```java
private int tempVarCounter = 0;           // Contador para variables temporales
private int quadCounter = 0;              // Contador de cuádruplos
private boolean insideFunction = false;   // Flag para contexto de función
private int currentParameterIndex = 0;    // Índice de parámetros actual
```

### Gestión de Ámbitos
```java
private Stack<Map<String, SemanticData.Variable>> symbolTableStack = new Stack<>();
private VirtualMemory memory = new VirtualMemory();
```

## Clase Interna Quadruple

### Estructura
```java
public static class Quadruple {
    String operator;  // Operador
    int arg1;         // Primer argumento (dirección virtual)
    int arg2;         // Segundo argumento (dirección virtual)
    int result;       // Resultado (dirección virtual)
}
```

### Constructores Especializados

#### Para Operaciones Binarias
```java
public Quadruple(String operator, int arg1, int arg2, int result)
```
Usado para: +, -, *, /, <, >, ==, etc.

#### Para Operaciones Unarias
```java
public Quadruple(String operator, int arg1)
```
Usado para: PRINT

#### Para Saltos
```java
public Quadruple(String operator, int arg1, int result)
```
Usado para: GOTO, GOTOF, GOTOT

#### Para Funciones
```java
public Quadruple(String operator, String functionName, int arg2, int result)
```
Usado para: ERA, GOSUB (convierte nombre a hash)

## Métodos Principales de Generación

### Constructor
```java
public GeneradorCuadruplosVisitor(Map<String, SemanticData.Variable> globalSymbolTable)
```
- Inicializa la pila de tablas de símbolos con la tabla global
- Prepara todas las estructuras de datos para la generación

### Gestión de Variables Temporales
```java
private String generateTempVar()
```
- Genera nombres únicos para variables temporales (t0, t1, t2, ...)
- Incrementa el contador automáticamente
- Retorna el nombre de la variable temporal

### Gestión de Direcciones Virtuales
```java
private int getAddressForOperand(String operand, String type)
```
**Flujo de Ejecución**:
1. Verifica si ya existe una dirección para el operando
2. Si es constante: obtiene dirección de constantes y almacena valor
3. Si es temporal: obtiene dirección temporal
4. Si es variable: determina si es global/local y obtiene dirección
5. Almacena la relación operando-dirección en el mapa
6. Retorna la dirección virtual

### Determinación de Tipos
```java
private String getType(String operand)
```
**Lógica de Determinación**:
- Números enteros: `\\d+` → "int"
- Números flotantes: `\\d+\\.\\d+` → "float"
- Strings: `"..."` → "str"
- Booleanos: "true"/"false" → "bool"
- Temporales: consulta pila de tipos o infiere por dirección
- Variables: busca en tabla de símbolos

## Métodos Visitor Principales

### visitProgram()
**Flujo**:
1. Procesa variables globales (`vars_opt`)
2. Procesa declaraciones de funciones (`funcs_p`)
3. Procesa el cuerpo principal (`body`)

### visitAssing()
**Generación de Cuádruplos**:
1. Procesa la expresión del lado derecho
2. Obtiene el resultado de la pila de operandos
3. Determina el tipo de la variable destino
4. Genera cuádruplo de asignación: `= addressExpr - addressVar`

### visitFuncs()
**Flujo Completo**:
1. Genera GOTO para saltar la función durante ejecución secuencial
2. Registra dirección de inicio de la función
3. Crea nueva tabla de símbolos local
4. Cuenta y procesa parámetros
5. Procesa variables locales
6. Procesa cuerpo de la función
7. Genera ENDFUNC
8. Limpia contexto local
9. Completa el GOTO inicial

### visitCondition()
**Generación de Cuádruplos**:
1. Procesa la expresión condicional
2. Genera GOTOF (salta si falso)
3. Procesa cuerpo del IF
4. Si hay ELSE: genera GOTO, procesa ELSE
5. Completa direcciones de salto

### visitCycle()
**Generación de Cuádruplos**:
1. Marca posición de inicio del ciclo
2. Procesa condición
3. Genera GOTOF (salta si falso para salir)
4. Procesa cuerpo del ciclo
5. Genera GOTO para volver al inicio
6. Completa GOTOF para salir del ciclo

### visitF_call()
**Generación de Cuádruplos**:
1. Procesa argumentos de la función
2. Genera ERA (Espacio de Registro de Activación)
3. Genera PARAM para cada argumento
4. Genera GOSUB con dirección de la función

### visitPrint()
**Generación de Cuádruplos**:
1. Procesa lista de expresiones
2. Genera cuádruplo PRINT para cada expresión
3. Mantiene orden correcto de impresión

## Generación de Expresiones

### Métodos de Expresiones
- `visitExpresion()`: Maneja operadores OR (||)
- `visitAndExpr()`: Maneja operadores AND (&&)
- `visitComparacion()`: Maneja operadores relacionales (<, >, ==, etc.)
- `visitAritmetica()`: Maneja operadores aditivos (+, -)
- `visitTermino()`: Maneja operadores multiplicativos (*, /)
- `visitFactor()`: Maneja factores (variables, constantes, paréntesis)

### generateQuadruple()
**Proceso de Generación**:
1. Extrae operador de la pila de operadores
2. Extrae dos operandos de la pila de operandos
3. Extrae tipos correspondientes
4. Determina tipo resultante
5. Genera variable temporal para resultado
6. Obtiene direcciones virtuales
7. Crea y almacena cuádruplo
8. Empuja resultado temporal a las pilas

### determineResultType()
**Reglas de Tipos**:
- Operadores relacionales/lógicos → "bool"
- Si algún operando es "float" → "float"
- Operaciones entre enteros → "int"

## Gestión de Funciones

### Conteo de Parámetros
```java
private int countParameters(LenguajeParser.AtributoContext ctx)
```
- Cuenta recursivamente parámetros en declaración de función
- Usado para configurar VirtualMemory correctamente

### Gestión de Ámbitos
- Empuja nueva tabla de símbolos al entrar a función
- Asigna direcciones consecutivas a parámetros
- Limpia direcciones globales al salir de función
- Mantiene separación entre contextos global y local

## Interacción con Otros Componentes

### Con VirtualMemory
- Obtiene direcciones virtuales para variables, temporales y constantes
- Configura número de parámetros para funciones
- Mantiene rangos de direcciones apropiados

### Con SemanticData
- Usa tabla de símbolos para información de variables
- Accede a información de tipos y declaraciones

### Con VirtualMachine
- Proporciona lista de cuádruplos para ejecución
- Proporciona mapa de valores de constantes
- Define el formato de código intermedio

## Métodos de Salida

### getQuadruples()
- Retorna copia de la lista de cuádruplos generados
- Usado por VirtualMachine para ejecución

### getConstantValues()
- Retorna mapa de constantes y sus valores
- Usado para inicializar memoria de constantes

### printQuadruples()
- Imprime todos los cuádruplos generados
- Útil para debugging y verificación

## Flujo de Ejecución Típico

### Fase de Inicialización
1. Se crea con tabla de símbolos global
2. Se inicializan todas las estructuras de datos
3. Se prepara VirtualMemory

### Fase de Generación
1. Recorre AST usando patrón Visitor
2. Genera cuádruplos para cada construcción
3. Mantiene pilas de operadores/operandos
4. Asigna direcciones virtuales dinámicamente

### Fase de Finalización
1. Completa saltos pendientes
2. Proporciona cuádruplos y constantes a VM
3. Lista para ejecución en Máquina Virtual

## Consideraciones de Diseño

### Eficiencia
- Uso de pilas para manejo de precedencia
- HashMap para acceso O(1) a direcciones
- Generación en una sola pasada del AST

### Flexibilidad
- Soporte para múltiples tipos de datos
- Manejo de funciones anidadas
- Extensible para nuevas operaciones

### Robustez
- Validación de tipos durante generación
- Manejo de ámbitos correctos
- Debugging integrado con mensajes informativos
