# Manual de VirtualMachine.java

## Propósito General
La clase `VirtualMachine` es el núcleo ejecutor del sistema de compilación. Su función principal es interpretar y ejecutar los cuádruplos generados por `GeneradorCuadruplosVisitor`, simulando una máquina virtual que puede ejecutar el código intermedio. Maneja memoria, control de flujo, llamadas a funciones y todas las operaciones del lenguaje.

## Arquitectura de la Máquina Virtual

### Componentes Principales
La VirtualMachine está compuesta por varios subsistemas que trabajan en conjunto:

1. **Sistema de Memoria** (`ExecutionMemory`)
2. **Sistema de Llamadas a Función** (`Stack<ActivationRecord>`)
3. **Control de Flujo** (Program Counter)
4. **Gestión de Parámetros** (`Stack<Object>`)

## Estructura de Datos

### Variables de Instancia Principales
```java
private ExecutionMemory memory;                                    // Memoria de ejecución
private Stack<ActivationRecord> callStack;                        // Pila de llamadas
private int programCounter;                                        // Contador de programa
private List<GeneradorCuadruplosVisitor.Quadruple> quadruples;    // Lista de cuádruplos
private Stack<Object> parameterStack;                             // Pila de parámetros
private Map<String, Integer> functionAddresses;                   // Direcciones de funciones
private boolean running;                                           // Flag de ejecución
private boolean debugMode;                                         // Modo debug
private ActivationRecord pendingActivationRecord;                 // Registro temporal para ERA/GOSUB
```

### Detalles de Componentes

#### ExecutionMemory memory
- **Propósito**: Gestiona memoria global, temporal y de constantes
- **Uso**: Almacena variables globales, resultados temporales y constantes
- **Interacción**: VirtualMachine delega operaciones de memoria no-local

#### Stack<ActivationRecord> callStack
- **Propósito**: Mantiene contexto de funciones activas
- **Estructura**: Pila LIFO para manejo de llamadas anidadas
- **Contenido**: Cada elemento contiene variables locales y dirección de retorno

#### int programCounter
- **Propósito**: Apunta al cuádruplo actual en ejecución
- **Rango**: 0 hasta quadruples.size() - 1
- **Modificación**: Incremento automático o saltos explícitos

#### Stack<Object> parameterStack
- **Propósito**: Almacena parámetros durante llamadas a función
- **Flujo**: PARAM empuja, GOSUB consume y asigna
- **Orden**: Los parámetros se almacenan en orden inverso

## Constructor y Inicialización

### Constructor
```java
public VirtualMachine()
```
**Inicialización**:
- Crea nueva instancia de `ExecutionMemory`
- Inicializa pila de llamadas vacía
- Inicializa pila de parámetros vacía
- Establece flags de control
- Prepara mapa de direcciones de funciones

## Método Principal de Ejecución

### execute()
```java
public void execute(List<GeneradorCuadruplosVisitor.Quadruple> quadruples, Map<Integer, Object> constantValues)
```

**Flujo de Ejecución**:
1. **Inicialización**:
   - Almacena lista de cuádruplos
   - Resetea program counter a 0
   - Establece flag running = true
   - Inicializa constantes en memoria

2. **Bucle Principal**:
   ```java
   while (running && programCounter < quadruples.size()) {
       GeneradorCuadruplosVisitor.Quadruple currentQuad = quadruples.get(programCounter);
       executeQuadruple(currentQuad);
   }
   ```

3. **Debug Opcional**:
   - Imprime cuádruplo actual si debugMode está activo
   - Muestra estado de memoria después de cada operación

### executeQuadruple()
**Propósito**: Ejecuta un cuádruplo individual

**Switch Principal**:
- **Operaciones Aritméticas**: +, -, *, /
- **Operaciones de Comparación**: >, <, ==, !=, >=, <=
- **Operaciones Lógicas**: &&, ||
- **Asignación**: =
- **Control de Flujo**: GOTO, GOTOF, GOTOT
- **E/S**: PRINT
- **Funciones**: ERA, PARAM, GOSUB, ENDFUNC

## Gestión de Memoria

### getMemoryValue()
**Propósito**: Obtener valor considerando contexto actual

**Lógica de Decisión**:
```java
if (address >= VirtualMemory.LOCAL_INT_START && address < VirtualMemory.TEMP_INT_START && !callStack.isEmpty()) {
    // Usar ActivationRecord para direcciones locales
    ActivationRecord currentRecord = callStack.peek();
    return currentRecord.getLocalValue(address);
} else {
    // Usar ExecutionMemory para direcciones globales/temporales/constantes
    return memory.getValue(address);
}
```

### setMemoryValue()
**Propósito**: Almacenar valor considerando contexto actual

**Misma Lógica**: Delega a ActivationRecord o ExecutionMemory según la dirección

## Operaciones Aritméticas

### Interfaz ArithmeticOperation
```java
@FunctionalInterface
private interface ArithmeticOperation {
    Object apply(Object a, Object b);
}
```

### performArithmetic()
**Flujo**:
1. Obtiene valores de los operandos usando `getMemoryValue()`
2. Valida que no sean null
3. Aplica la operación usando la interfaz funcional
4. Almacena resultado usando `setMemoryValue()`
5. Debug opcional

**Soporte de Tipos**:
- **Enteros**: Operaciones directas entre Integer
- **Flotantes**: Conversión automática a Float
- **Strings**: Concatenación para operador +
- **Validación**: Lanza excepciones para tipos incompatibles

## Operaciones de Comparación

### Interfaz ComparisonOperation
```java
@FunctionalInterface
private interface ComparisonOperation {
    Boolean apply(Object a, Object b);
}
```

### performComparison()
**Validaciones**:
- Verifica que los valores no sean null
- Valida que sean números (excepto para == y !=)
- Convierte a Number para comparaciones numéricas

**Operadores Soportados**:
- `>`, `<`, `>=`, `<=`: Comparaciones numéricas
- `==`, `!=`: Comparaciones de igualdad (cualquier tipo)

## Control de Flujo

### GOTO
```java
programCounter = quad.result;
return; // No incrementar PC
```

### GOTOF (Goto if False)
```java
private void performGotoF(GeneradorCuadruplosVisitor.Quadruple quad) {
    Object condition = getMemoryValue(quad.arg1);
    if (condition instanceof Boolean && !(Boolean) condition) {
        programCounter = quad.result;
        return;
    } else {
        programCounter++;
    }
}
```

### GOTOT (Goto if True)
- Similar a GOTOF pero salta si la condición es verdadera

## Gestión de Funciones

### ERA (Espacio de Registro de Activación)
```java
private void performERA(GeneradorCuadruplosVisitor.Quadruple quad) {
    String functionName = "func_" + quad.arg1;
    int baseAddress = VirtualMemory.LOCAL_INT_START + (callStack.size() * 1000);
    pendingActivationRecord = new ActivationRecord(functionName, -1, baseAddress);
}
```

**Características**:
- Crea ActivationRecord temporal
- Calcula dirección base basada en nivel de anidamiento
- No establece dirección de retorno (se hace en GOSUB)

### PARAM (Paso de Parámetros)
```java
private void performParam(GeneradorCuadruplosVisitor.Quadruple quad) {
    Object paramValue = getMemoryValue(quad.arg1);
    parameterStack.push(paramValue);
}
```

**Flujo**:
- Obtiene valor del parámetro de memoria
- Lo empuja a la pila de parámetros
- Se acumulan hasta GOSUB

### GOSUB (Llamada a Subrutina)
**Flujo Completo**:
1. **Validación**: Verifica que exista pendingActivationRecord
2. **Asignación de Parámetros**:
   ```java
   List<Object> params = new ArrayList<>();
   while (!parameterStack.isEmpty()) {
       params.add(parameterStack.pop());
   }
   // Asignar en orden correcto (invertir lista)
   for (int i = params.size() - 1; i >= 0; i--) {
       pendingActivationRecord.setParameter(paramIndex, paramValue);
   }
   ```
3. **Configuración de Retorno**: `setReturnAddress(programCounter + 1)`
4. **Activación**: Empuja registro a callStack
5. **Salto**: `programCounter = quad.arg1`

### ENDFUNC (Final de Función)
```java
private void performEndFunc() {
    if (!callStack.isEmpty()) {
        ActivationRecord record = callStack.pop();
        programCounter = record.getReturnAddress();
        return;
    } else {
        running = false;
    }
}
```

## Operaciones de E/S

### PRINT
```java
private void performPrint(GeneradorCuadruplosVisitor.Quadruple quad) {
    Object value = getMemoryValue(quad.arg1);
    System.out.println("OUTPUT: " + value);
}
```

## Métodos de Utilidad

### setDebugMode()
- Habilita/deshabilita modo debug
- Controla verbosidad de la ejecución

### stop()
- Detiene ejecución estableciendo running = false
- Permite terminación controlada

### printMemoryState()
- Imprime estado de memoria global y temporal
- Útil para debugging y análisis

## Flujo de Ejecución Típico

### Programa Simple
1. **Inicialización**: Cargar cuádruplos y constantes
2. **Ejecución Secuencial**: Procesar cuádruplos uno por uno
3. **Finalización**: Terminar cuando se agoten cuádruplos

### Programa con Funciones
1. **Fase Principal**: Ejecutar código principal
2. **Llamada a Función**:
   - ERA: Preparar espacio
   - PARAM: Pasar parámetros
   - GOSUB: Saltar a función
3. **Ejecución de Función**: Procesar cuerpo de función
4. **Retorno**: ENDFUNC regresa al punto de llamada
5. **Continuación**: Seguir con código principal

### Programa con Condicionales
1. **Evaluación**: Procesar expresión condicional
2. **Decisión**: GOTOF/GOTOT según resultado
3. **Ejecución**: Procesar rama correspondiente
4. **Reunión**: Continuar después del condicional

## Manejo de Errores

### Tipos de Errores
- **Null Values**: Valores no inicializados en operaciones
- **Type Mismatches**: Tipos incompatibles en operaciones
- **Division by Zero**: División por cero
- **Invalid Operations**: Operadores no soportados
- **Stack Underflow**: Llamadas sin contexto apropiado

### Estrategia de Manejo
- Excepciones descriptivas con contexto
- Información de debugging incluida
- Terminación controlada del programa

## Consideraciones de Diseño

### Eficiencia
- Acceso directo a cuádruplos por índice
- Uso de HashMap para direcciones de funciones
- Pilas para gestión LIFO eficiente

### Flexibilidad
- Soporte para múltiples tipos de datos
- Extensible para nuevas operaciones
- Modo debug configurable

### Robustez
- Validación exhaustiva de operaciones
- Manejo de casos edge
- Recuperación de errores cuando es posible
