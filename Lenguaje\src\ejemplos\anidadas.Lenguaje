programa ejemploAnidadoFunciones ;
var contadorGlobal : int ;

func void saludar (nombre: str) [ {
    print ("Hola") ;
    print (nombre) ;
    print (" ") ;
    contadorGlobal = contadorGlobal + 1 ;
} ] ;

func void interaccion (mensaje: str) [ {
    var nombreUsuario : str ;
    nombreUsuario = "Mundo" ;

    print (mensaje) ;
    saludar (nombreUsuario) ; 
    print ("Contador global ahora es") ;
    print (contadorGlobal) ;
} ] ;

main {
    contadorGlobal = 0 ; 
    interaccion ("Iniciando interacción de funciones anidadas") ;

    print ("") ;
    print ("Fin del programa") ;
    print ("Contador global final") ;
    print (contadorGlobal) ;
} end